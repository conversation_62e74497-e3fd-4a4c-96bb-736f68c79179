<?php

namespace App\Controllers\Api;

use App\Models\ServiceModel;
use CodeIgniter\HTTP\ResponseInterface;

class Services extends BaseController
{
    protected $serviceModel;

    public function __construct()
    {
        $this->serviceModel = new ServiceModel();
    }

    public function index()
    {
        $services = $this->serviceModel->where('status', 'active')->findAll();

        foreach ($services as &$service) {
            if ($service['image']) {
                $service['image_url'] = base_url('public/uploads/services/' . $service['image']);
            }
        }

        return $this->respondWithSuccess($services, 'Services retrieved successfully');
    }

    public function show($id = null)
    {
        $service = $this->serviceModel->find($id);

        if (!$service) {
            return $this->respondWithError('Service not found', ResponseInterface::HTTP_NOT_FOUND);
        }

        if ($service['image']) {
            $service['image_url'] = base_url('public/uploads/services/' . $service['image']);
        }

        // Add full description and features for the detailed view
        // In a real application, these would come from the database
        $service['full_description'] = 'Our ' . $service['title'] . ' practice covers a wide range of services. ' . $service['description'];

        // Example features based on the service type
        $features = [];
        switch ($id) {
            case 1: // Civil Litigation
                $features = [
                    'Property disputes',
                    'Contract disputes',
                    'Landlord-tenant issues',
                    'Consumer protection',
                    'Debt recovery'
                ];
                break;
            case 2: // Criminal Defense
                $features = [
                    'Criminal trials',
                    'Bail applications',
                    'Appeals',
                    'Legal representation',
                    'Case analysis'
                ];
                break;
            case 3: // Family Law
                $features = [
                    'Divorce proceedings',
                    'Child custody',
                    'Alimony',
                    'Property division',
                    'Domestic violence cases'
                ];
                break;
            case 4: // Corporate Law
                $features = [
                    'Business contracts',
                    'Mergers and acquisitions',
                    'Corporate compliance',
                    'Business formation',
                    'Legal documentation'
                ];
                break;
            case 5: // Tax Consultancy
                $features = [
                    'Tax disputes',
                    'Financial planning',
                    'Tax compliance',
                    'Audit representation',
                    'Tax appeals'
                ];
                break;
            case 6: // Legal Documentation
                $features = [
                    'Contract drafting',
                    'Agreement review',
                    'Legal document preparation',
                    'Notarization services',
                    'Document verification'
                ];
                break;
            default:
                $features = [
                    'Consultation',
                    'Legal advice',
                    'Documentation',
                    'Representation',
                    'Case management'
                ];
        }

        $service['features'] = $features;

        return $this->respondWithSuccess($service, 'Service retrieved successfully');
    }

    public function create()
    {
        $rules = $this->serviceModel->validationRules;

        if (!$this->validate($rules)) {
            return $this->respondWithError($this->validator->getErrors());
        }

        // Handle file upload
        $image = $this->request->getFile('image');
        $imageName = null;

        if ($image && $image->isValid() && !$image->hasMoved()) {
            $imageName = $image->getRandomName();
            $image->move(ROOTPATH . 'public/uploads/services', $imageName);
        }

        $data = [
            'title'  => $this->request->getPost('title'),
            'image'  => $imageName,
            'status' => $this->request->getPost('status') ?? 'active',
        ];

        $id = $this->serviceModel->insert($data);
        $service = $this->serviceModel->find($id);

        return $this->respondWithSuccess($service, 'Service created successfully', ResponseInterface::HTTP_CREATED);
    }

    public function update($id = null)
    {
        $service = $this->serviceModel->find($id);

        if (!$service) {
            return $this->respondWithError('Service not found', ResponseInterface::HTTP_NOT_FOUND);
        }

        $rules = $this->serviceModel->validationRules;

        if (!$this->validate($rules)) {
            return $this->respondWithError($this->validator->getErrors());
        }

        // Handle file upload
        $image = $this->request->getFile('image');
        $imageName = $service['image'];

        if ($image && $image->isValid() && !$image->hasMoved()) {
            // Delete old image if exists
            if ($service['image'] && file_exists(ROOTPATH . 'public/uploads/services/' . $service['image'])) {
                unlink(ROOTPATH . 'public/uploads/services/' . $service['image']);
            }

            $imageName = $image->getRandomName();
            $image->move(ROOTPATH . 'public/uploads/services', $imageName);
        }

        $data = [
            'title'  => $this->request->getPost('title'),
            'image'  => $imageName,
            'status' => $this->request->getPost('status'),
        ];

        $this->serviceModel->update($id, $data);
        $service = $this->serviceModel->find($id);

        return $this->respondWithSuccess($service, 'Service updated successfully');
    }

    public function delete($id = null)
    {
        $service = $this->serviceModel->find($id);

        if (!$service) {
            return $this->respondWithError('Service not found', ResponseInterface::HTTP_NOT_FOUND);
        }

        // Delete image if exists
        if ($service['image'] && file_exists(ROOTPATH . 'public/uploads/services/' . $service['image'])) {
            unlink(ROOTPATH . 'public/uploads/services/' . $service['image']);
        }

        $this->serviceModel->delete($id);

        return $this->respondWithSuccess(null, 'Service deleted successfully');
    }
}
