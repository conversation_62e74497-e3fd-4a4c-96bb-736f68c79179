<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Auth::login');

// Auth Routes
$routes->get('auth/login', 'Auth::login');
$routes->post('auth/attemptLogin', 'Auth::attemptLogin');
$routes->get('auth/logout', 'Auth::logout');
$routes->get('auth/setup', 'Auth::setup');
$routes->get('auth/forgotPassword', 'Auth::forgotPassword');
$routes->post('auth/processForgotPassword', 'Auth::processForgotPassword');
$routes->get('auth/resetPassword/(:any)', 'Auth::resetPassword/$1');
$routes->post('auth/processResetPassword', 'Auth::processResetPassword');

// Admin Dashboard
$routes->get('admin', 'Admin::index');

// Revenue Report Routes
$routes->get('revenue-reports', 'RevenueReport::index');
$routes->get('revenue-reports/monthly/(:num)/(:num)', 'RevenueReport::monthly/$1/$2');
$routes->get('revenue-reports/calculate', 'RevenueReport::calculate');

// CKEditor Upload
$routes->post('upload/ckeditor', 'CKEditorUpload::upload');
$routes->options('upload/ckeditor', 'CKEditorUpload::upload');

// Home Slider Routes
$routes->get('home-slider', 'HomeSlider::index');
$routes->get('home-slider/new', 'HomeSlider::new');
$routes->post('home-slider/create', 'HomeSlider::create');
$routes->get('home-slider/edit/(:num)', 'HomeSlider::edit/$1');
$routes->post('home-slider/update/(:num)', 'HomeSlider::update/$1');
$routes->get('home-slider/delete/(:num)', 'HomeSlider::delete/$1');

// Home Meta Routes
$routes->get('home-meta', 'HomeMeta::index');
$routes->get('home-meta/new', 'HomeMeta::new');
$routes->post('home-meta/create', 'HomeMeta::create');
$routes->get('home-meta/edit/(:num)', 'HomeMeta::edit/$1');
$routes->post('home-meta/update/(:num)', 'HomeMeta::update/$1');

// Profile Routes
$routes->get('profile', 'Profile::index');
$routes->get('profile/edit', 'Profile::edit');
$routes->post('profile/update', 'Profile::update');
$routes->get('profile/change-password', 'Profile::changePassword');
$routes->post('profile/update-password', 'Profile::updatePassword');





// Services Routes
$routes->get('services', 'Services::index');
$routes->get('services/new', 'Services::new');
$routes->post('services/create', 'Services::create');
$routes->get('services/edit/(:num)', 'Services::edit/$1');
$routes->post('services/update/(:num)', 'Services::update/$1');
$routes->get('services/delete/(:num)', 'Services::delete/$1');

// Team Routes
$routes->get('team', 'Team::index');
$routes->get('team/new', 'Team::new');
$routes->post('team/create', 'Team::create');
$routes->get('team/edit/(:num)', 'Team::edit/$1');
$routes->post('team/update/(:num)', 'Team::update/$1');
$routes->get('team/delete/(:num)', 'Team::delete/$1');

// Gallery Routes
$routes->get('gallery', 'Gallery::index');
$routes->get('gallery/new', 'Gallery::new');
$routes->post('gallery/create', 'Gallery::create');
$routes->get('gallery/edit/(:num)', 'Gallery::edit/$1');
$routes->post('gallery/update/(:num)', 'Gallery::update/$1');
$routes->get('gallery/delete/(:num)', 'Gallery::delete/$1');

// Contact Routes
$routes->get('contact', 'Contact::index');
$routes->get('contact/view/(:num)', 'Contact::view/$1');
$routes->get('contact/delete/(:num)', 'Contact::delete/$1');
$routes->post('contact/submit', 'Contact::submit');

// Contact Info Routes
$routes->get('contact-info', 'ContactInfo::index');
$routes->get('contact-info/edit', 'ContactInfo::edit');
$routes->post('contact-info/update', 'ContactInfo::update');

// About Routes
$routes->get('about', 'About::index');
$routes->get('about/edit/(:num)', 'About::edit/$1');
$routes->post('about/update/(:num)', 'About::update/$1');

// About Journey Routes
$routes->get('about-journey', 'AboutJourney::index');
$routes->get('about-journey/new', 'AboutJourney::new');
$routes->post('about-journey/create', 'AboutJourney::create');
$routes->get('about-journey/edit/(:num)', 'AboutJourney::edit/$1');
$routes->post('about-journey/update/(:num)', 'AboutJourney::update/$1');
$routes->get('about-journey/delete/(:num)', 'AboutJourney::delete/$1');

// About Images Routes
$routes->get('about-images', 'AboutImage::index');
$routes->get('about-images/new', 'AboutImage::new');
$routes->post('about-images/create', 'AboutImage::create');
$routes->get('about-images/edit/(:num)', 'AboutImage::edit/$1');
$routes->post('about-images/update/(:num)', 'AboutImage::update/$1');
$routes->get('about-images/delete/(:num)', 'AboutImage::delete/$1');

// Client Routes
$routes->get('clients', 'Client::index');
$routes->get('clients/new', 'Client::new');
$routes->post('clients/create', 'Client::create');
$routes->get('clients/edit/(:num)', 'Client::edit/$1');
$routes->post('clients/update/(:num)', 'Client::update/$1');
$routes->get('clients/delete/(:num)', 'Client::delete/$1');

// Invoice Routes
$routes->get('invoices', 'Invoice::index');
$routes->get('invoices/new', 'Invoice::new');
$routes->post('invoices/create', 'Invoice::create');
$routes->get('invoices/edit/(:num)', 'Invoice::edit/$1');
$routes->post('invoices/update/(:num)', 'Invoice::update/$1');
$routes->get('invoices/view/(:num)', 'Invoice::view/$1');
$routes->get('invoices/print/(:num)', 'Invoice::print/$1');
$routes->get('invoices/delete/(:num)', 'Invoice::delete/$1');

// Blog Routes
$routes->get('blog-categories', 'BlogCategory::index');
$routes->get('blog-categories/new', 'BlogCategory::new');
$routes->post('blog-categories/create', 'BlogCategory::create');
$routes->get('blog-categories/edit/(:num)', 'BlogCategory::edit/$1');
$routes->post('blog-categories/update/(:num)', 'BlogCategory::update/$1');
$routes->get('blog-categories/delete/(:num)', 'BlogCategory::delete/$1');
$routes->post('blog-categories/generate-slug', 'BlogCategory::generateSlug');

$routes->get('blog-tags', 'BlogTag::index');
$routes->get('blog-tags/new', 'BlogTag::new');
$routes->post('blog-tags/create', 'BlogTag::create');
$routes->get('blog-tags/edit/(:num)', 'BlogTag::edit/$1');
$routes->post('blog-tags/update/(:num)', 'BlogTag::update/$1');
$routes->get('blog-tags/delete/(:num)', 'BlogTag::delete/$1');
$routes->post('blog-tags/generate-slug', 'BlogTag::generateSlug');

$routes->get('blog-posts', 'BlogPost::index');
$routes->get('blog-posts/new', 'BlogPost::new');
$routes->post('blog-posts/create', 'BlogPost::create');
$routes->get('blog-posts/edit/(:num)', 'BlogPost::edit/$1');
$routes->post('blog-posts/update/(:num)', 'BlogPost::update/$1');
$routes->get('blog-posts/delete/(:num)', 'BlogPost::delete/$1');
$routes->post('blog-posts/generate-slug', 'BlogPost::generateSlug');
$routes->get('blog-posts/comments/(:num)', 'BlogPost::comments/$1');
$routes->post('blog-posts/update-comment-status/(:num)', 'BlogPost::updateCommentStatus/$1');
$routes->get('blog-posts/delete-comment/(:num)', 'BlogPost::deleteComment/$1');

$routes->get('blog-comments', 'BlogComment::index');
$routes->get('blog-comments/edit/(:num)', 'BlogComment::edit/$1');
$routes->post('blog-comments/update/(:num)', 'BlogComment::update/$1');
$routes->get('blog-comments/delete/(:num)', 'BlogComment::delete/$1');
$routes->post('blog-comments/update-status/(:num)', 'BlogComment::updateStatus/$1');
$routes->post('blog-comments/bulk-action', 'BlogComment::bulkAction');

// API Routes
$routes->group('api', ['namespace' => 'App\Controllers\Api'], function($routes) {
    // CORS Test endpoint
    $routes->get('test', 'Test::index');
    $routes->options('test', 'Test::index');

    // Home API
    $routes->get('home/slider', 'Home::slider');
    $routes->options('home/slider', 'Home::slider');

    // Services API
    $routes->get('services', 'Services::index');
    $routes->get('services/(:num)', 'Services::show/$1');
    $routes->options('services', 'Services::index');
    $routes->options('services/(:num)', 'Services::show/$1');

    // Team API
    $routes->get('team', 'Team::index');
    $routes->get('team/(:num)', 'Team::show/$1');
    $routes->options('team', 'Team::index');
    $routes->options('team/(:num)', 'Team::show/$1');

    // Gallery API
    $routes->get('gallery', 'Gallery::index');
    $routes->get('gallery/(:num)', 'Gallery::show/$1');
    $routes->options('gallery', 'Gallery::index');
    $routes->options('gallery/(:num)', 'Gallery::show/$1');

    // Contact API
    $routes->get('contact', 'Contact::index');
    $routes->get('contact/messages', 'Contact::messages');
    $routes->get('contact/(:num)', 'Contact::show/$1');
    $routes->post('contact/submit', 'Contact::submit');
    $routes->options('contact', 'Contact::index');
    $routes->options('contact/messages', 'Contact::messages');
    $routes->options('contact/(:num)', 'Contact::show/$1');
    $routes->options('contact/submit', 'Contact::submit');

    // About API
    $routes->get('about', 'About::index');
    $routes->get('about/journey', 'About::journey');
    $routes->get('about/images', 'About::images');
    $routes->options('about', 'About::index');
    $routes->options('about/journey', 'About::journey');
    $routes->options('about/images', 'About::images');

    // Blog API
    $routes->get('blog/posts', 'Blog::posts');
    $routes->get('blog/post/(:segment)', 'Blog::post/$1');
    $routes->get('blog/categories', 'Blog::categories');
    $routes->get('blog/tags', 'Blog::tags');
    $routes->get('blog/popular-posts', 'Blog::popularPosts');
    $routes->get('blog/recent-posts', 'Blog::recentPosts');
    $routes->post('blog/submit-comment', 'Blog::submitComment');
    $routes->get('blog/search', 'Blog::search');
    $routes->options('blog/posts', 'Blog::posts');
    $routes->options('blog/post/(:segment)', 'Blog::post/$1');
    $routes->options('blog/categories', 'Blog::categories');
    $routes->options('blog/tags', 'Blog::tags');
    $routes->options('blog/popular-posts', 'Blog::popularPosts');
    $routes->options('blog/recent-posts', 'Blog::recentPosts');
    $routes->options('blog/submit-comment', 'Blog::submitComment');
    $routes->options('blog/search', 'Blog::search');
});
