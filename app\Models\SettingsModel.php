<?php

namespace App\Models;

use CodeIgniter\Model;

class SettingsModel extends Model
{
    protected $table            = 'settings';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'user_id', 'email_notifications', 'push_notifications', 
        'theme', 'language', 'created_at', 'updated_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules      = [
        'user_id' => 'required|integer',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    /**
     * Get user settings or create default if not exists
     */
    public function getUserSettings($userId)
    {
        $settings = $this->where('user_id', $userId)->first();

        if (!$settings) {
            $data = [
                'user_id'             => $userId,
                'email_notifications' => 1,
                'push_notifications'  => 1,
                'theme'               => 'light',
                'language'            => 'en'
            ];

            $this->insert($data);
            $settings = $this->where('user_id', $userId)->first();
        }

        return $settings;
    }

    /**
     * Update user settings
     */
    public function updateUserSettings($userId, $data)
    {
        $settings = $this->where('user_id', $userId)->first();

        if (!$settings) {
            $data['user_id'] = $userId;
            return $this->insert($data);
        }

        return $this->update($settings['id'], $data);
    }
}
