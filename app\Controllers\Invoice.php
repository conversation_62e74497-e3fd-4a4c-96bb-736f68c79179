<?php

namespace App\Controllers;

use App\Models\InvoiceModel;
use App\Models\InvoiceItemModel;
use App\Models\ClientModel;
use CodeIgniter\Exceptions\PageNotFoundException;

class Invoice extends BaseController
{
    protected $invoiceModel;
    protected $invoiceItemModel;
    protected $clientModel;

    public function __construct()
    {
        $this->invoiceModel = new InvoiceModel();
        $this->invoiceItemModel = new InvoiceItemModel();
        $this->clientModel = new ClientModel();
    }

    public function index()
    {
        // Get the current page from the URL
        $currentPage = $this->request->getVar('page') ? (int) $this->request->getVar('page') : 1;

        // Get the total count of invoices
        $totalInvoices = $this->invoiceModel->countAllResults();

        // Set the number of items per page
        $perPage = 10;

        // Calculate the offset
        $offset = ($currentPage - 1) * $perPage;

        // Get paginated invoices with client details
        $invoices = $this->invoiceModel->getAllInvoicesWithClientsPaginated($perPage, $offset);

        // Create a custom pager
        $pager = service('pager');
        $pager->setPath('invoices');
        $pager->makeLinks($currentPage, $perPage, $totalInvoices, 'admin_full');

        $data = [
            'title'    => 'Invoices',
            'invoices' => $invoices,
            'pager'    => $pager,
            'perPage'  => $perPage,
            'total'    => $totalInvoices,
            'page'     => $currentPage,
        ];

        return view('admin/invoices/index', $data);
    }

    public function new()
    {
        $data = [
            'title'   => 'Create New Invoice',
            'clients' => $this->clientModel->orderBy('name', 'ASC')->findAll(),
            'invoice_number' => $this->invoiceModel->generateInvoiceNumber(),
        ];

        return view('admin/invoices/new', $data);
    }

    public function create()
    {
        // Manual validation
        $invoiceNumber = $this->request->getPost('invoice_number');
        $clientId = $this->request->getPost('client_id');
        $invoiceDate = $this->request->getPost('invoice_date');
        $subject = $this->request->getPost('subject');

        $errors = [];

        // Check required fields
        if (empty($invoiceNumber)) {
            $errors['invoice_number'] = 'The invoice number field is required.';
        } elseif (strlen($invoiceNumber) > 50) {
            $errors['invoice_number'] = 'The invoice number field cannot exceed 50 characters in length.';
        } else {
            // Check uniqueness
            $existingInvoice = $this->invoiceModel->where('invoice_number', $invoiceNumber)->first();
            if ($existingInvoice) {
                $errors['invoice_number'] = 'The invoice number field must contain a unique value.';
            }
        }

        if (empty($clientId)) {
            $errors['client_id'] = 'The client field is required.';
        }

        if (empty($invoiceDate)) {
            $errors['invoice_date'] = 'The invoice date field is required.';
        }

        if (empty($subject)) {
            $errors['subject'] = 'The subject field is required.';
        } elseif (strlen($subject) > 255) {
            $errors['subject'] = 'The subject field cannot exceed 255 characters in length.';
        }

        // If there are validation errors, redirect back with errors
        if (!empty($errors)) {
            return redirect()->back()->withInput()->with('errors', $errors);
        }

        // Get the form data
        $invoiceData = [
            'invoice_number'  => $this->request->getPost('invoice_number'),
            'client_id'       => $this->request->getPost('client_id'),
            'invoice_date'    => $this->request->getPost('invoice_date'),
            'due_date'        => $this->request->getPost('due_date') ?: null,
            'subject'         => $this->request->getPost('subject'),
            'status'          => $this->request->getPost('status'),
            'notes'           => $this->request->getPost('notes'),
            'upi_id'          => $this->request->getPost('upi_id'),
        ];

        // Handle QR code upload
        $qrCode = $this->request->getFile('qr_code');
        if ($qrCode && $qrCode->isValid() && !$qrCode->hasMoved()) {
            $newName = $qrCode->getRandomName();
            $qrCode->move(ROOTPATH . 'public/uploads/qr_codes', $newName);
            $invoiceData['qr_code'] = $newName;
        }

        // Signature is now permanent and not uploaded

        // Get the invoice items
        $descriptions = $this->request->getPost('description');
        $fees = $this->request->getPost('fee');

        // Calculate subtotal
        $subtotal = 0;
        $items = [];

        if (is_array($descriptions)) {
            for ($i = 0; $i < count($descriptions); $i++) {
                if (!empty($descriptions[$i]) && !empty($fees[$i])) {
                    $fee = floatval($fees[$i]);

                    $items[] = [
                        'description' => $descriptions[$i],
                        'fee'         => $fee,
                    ];

                    $subtotal += $fee;
                }
            }
        }

        // Add subtotal and total to invoice data
        $invoiceData['subtotal'] = $subtotal;
        $invoiceData['total'] = $subtotal; // Add tax calculation if needed

        // Start a database transaction
        $db = \Config\Database::connect();
        $db->transBegin();

        try {
            // Insert the invoice - skip validation since we've already done it manually
            $this->invoiceModel->skipValidation(true);
            $invoiceId = $this->invoiceModel->insert($invoiceData);

            if (!$invoiceId) {
                // If insert fails, rollback and return error
                $db->transRollback();
                return redirect()->back()->withInput()->with('error', 'Failed to create invoice. Database error occurred.');
            }

            // Insert invoice items
            foreach ($items as $item) {
                $item['invoice_id'] = $invoiceId;
                $this->invoiceItemModel->insert($item);
            }

            // Commit the transaction
            if ($db->transStatus() === false) {
                $db->transRollback();
                return redirect()->back()->withInput()->with('error', 'Failed to create invoice items. Please try again.');
            } else {
                $db->transCommit();

                // Get client name for notification
                $client = $this->clientModel->find($invoiceData['client_id']);
                $clientName = $client ? $client['name'] : 'Unknown Client';

                // Create notification for the current user
                $userId = session()->get('id');
                create_notification(
                    $userId,
                    'invoice',
                    'New Invoice Created',
                    'Invoice #' . $invoiceData['invoice_number'] . ' for ' . $clientName . ' has been created',
                    site_url('invoices/view/' . $invoiceId),
                    'bi-receipt'
                );

                return redirect()->to('invoices')->with('message', 'Invoice created successfully.');
            }
        } catch (\Exception $e) {
            $db->transRollback();
            return redirect()->back()->withInput()->with('error', 'An error occurred: ' . $e->getMessage());
        }
    }

    public function edit($id = null)
    {
        $invoice = $this->invoiceModel->find($id);

        if (!$invoice) {
            throw new PageNotFoundException('Invoice not found');
        }

        $data = [
            'title'    => 'Edit Invoice',
            'invoice'  => $invoice,
            'items'    => $this->invoiceItemModel->getItemsByInvoiceId($id),
            'clients'  => $this->clientModel->orderBy('name', 'ASC')->findAll(),
        ];

        return view('admin/invoices/edit', $data);
    }

    public function update($id = null)
    {
        $invoice = $this->invoiceModel->find($id);

        if (!$invoice) {
            throw new PageNotFoundException('Invoice not found');
        }

        // Skip validation entirely and handle it manually
        $newInvoiceNumber = $this->request->getPost('invoice_number');
        $clientId = $this->request->getPost('client_id');
        $invoiceDate = $this->request->getPost('invoice_date');
        $subject = $this->request->getPost('subject');

        // Manual validation
        $errors = [];

        // Check required fields
        if (empty($newInvoiceNumber)) {
            $errors['invoice_number'] = 'The invoice number field is required.';
        } elseif (strlen($newInvoiceNumber) > 50) {
            $errors['invoice_number'] = 'The invoice number field cannot exceed 50 characters in length.';
        }

        if (empty($clientId)) {
            $errors['client_id'] = 'The client field is required.';
        }

        if (empty($invoiceDate)) {
            $errors['invoice_date'] = 'The invoice date field is required.';
        }

        if (empty($subject)) {
            $errors['subject'] = 'The subject field is required.';
        } elseif (strlen($subject) > 255) {
            $errors['subject'] = 'The subject field cannot exceed 255 characters in length.';
        }

        // Only check uniqueness if the invoice number has changed
        if ($newInvoiceNumber !== $invoice['invoice_number']) {
            // Check if the new invoice number already exists
            $existingInvoice = $this->invoiceModel->where('invoice_number', $newInvoiceNumber)
                                                 ->where('id !=', $id)
                                                 ->first();

            if ($existingInvoice) {
                $errors['invoice_number'] = 'The invoice number field must contain a unique value.';
            }
        }

        // If there are validation errors, redirect back with errors
        if (!empty($errors)) {
            return redirect()->back()->withInput()->with('errors', $errors);
        }

        // Get the form data
        $invoiceData = [
            'invoice_number'  => $this->request->getPost('invoice_number'),
            'client_id'       => $this->request->getPost('client_id'),
            'invoice_date'    => $this->request->getPost('invoice_date'),
            'due_date'        => $this->request->getPost('due_date') ?: null,
            'subject'         => $this->request->getPost('subject'),
            'status'          => $this->request->getPost('status'),
            'notes'           => $this->request->getPost('notes'),
            'upi_id'          => $this->request->getPost('upi_id'),
        ];

        // Handle QR code upload
        $qrCode = $this->request->getFile('qr_code');
        if ($qrCode && $qrCode->isValid() && !$qrCode->hasMoved()) {
            // Delete old QR code if exists
            if (!empty($invoice['qr_code']) && file_exists(ROOTPATH . 'public/uploads/qr_codes/' . $invoice['qr_code'])) {
                unlink(ROOTPATH . 'public/uploads/qr_codes/' . $invoice['qr_code']);
            }

            $newName = $qrCode->getRandomName();
            $qrCode->move(ROOTPATH . 'public/uploads/qr_codes', $newName);
            $invoiceData['qr_code'] = $newName;
        }

        // Signature is now permanent and not uploaded

        // Get the invoice items
        $descriptions = $this->request->getPost('description');
        $fees = $this->request->getPost('fee');

        // Calculate subtotal
        $subtotal = 0;
        $items = [];

        if (is_array($descriptions)) {
            for ($i = 0; $i < count($descriptions); $i++) {
                if (!empty($descriptions[$i]) && !empty($fees[$i])) {
                    $fee = floatval($fees[$i]);

                    $items[] = [
                        'invoice_id'  => $id,
                        'description' => $descriptions[$i],
                        'fee'         => $fee,
                    ];

                    $subtotal += $fee;
                }
            }
        }

        // Add subtotal and total to invoice data
        $invoiceData['subtotal'] = $subtotal;
        $invoiceData['total'] = $subtotal; // Add tax calculation if needed

        // Start a database transaction
        $db = \Config\Database::connect();
        $db->transBegin();

        try {
            // Update the invoice - skip validation since we've already done it manually
            $this->invoiceModel->skipValidation(true);
            $result = $this->invoiceModel->update($id, $invoiceData);

            if (!$result) {
                // If update fails, rollback and return error
                $db->transRollback();
                return redirect()->back()->withInput()->with('error', 'Failed to update invoice. Database error occurred.');
            }

            // Delete existing items
            $this->invoiceItemModel->where('invoice_id', $id)->delete();

            // Insert new items
            foreach ($items as $item) {
                $this->invoiceItemModel->insert($item);
            }

            // Commit the transaction
            if ($db->transStatus() === false) {
                $db->transRollback();
                return redirect()->back()->withInput()->with('error', 'Failed to update invoice items. Please try again.');
            } else {
                $db->transCommit();
                return redirect()->to('invoices')->with('message', 'Invoice updated successfully.');
            }
        } catch (\Exception $e) {
            $db->transRollback();
            return redirect()->back()->withInput()->with('error', 'An error occurred: ' . $e->getMessage());
        }
    }

    public function view($id = null)
    {
        $invoice = $this->invoiceModel->getInvoiceWithDetails($id);

        if (!$invoice) {
            throw new PageNotFoundException('Invoice not found');
        }

        $data = [
            'title'   => 'View Invoice',
            'invoice' => $invoice,
            'items'   => $this->invoiceItemModel->getItemsByInvoiceId($id),
        ];

        return view('admin/invoices/view', $data);
    }

    public function delete($id = null)
    {
        $invoice = $this->invoiceModel->find($id);

        if (!$invoice) {
            throw new PageNotFoundException('Invoice not found');
        }

        // Invoice items will be deleted automatically due to foreign key constraint

        $this->invoiceModel->delete($id);

        return redirect()->to('invoices')->with('message', 'Invoice deleted successfully.');
    }

    public function print($id = null)
    {
        $invoice = $this->invoiceModel->getInvoiceWithDetails($id);

        if (!$invoice) {
            throw new PageNotFoundException('Invoice not found');
        }

        $data = [
            'invoice' => $invoice,
            'items'   => $this->invoiceItemModel->getItemsByInvoiceId($id),
            'print_mode' => true
        ];

        // Simply render the print view
        return view('admin/invoices/print', $data);
    }


}
