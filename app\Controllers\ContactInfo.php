<?php

namespace App\Controllers;

use App\Models\ContactInfoModel;
use CodeIgniter\Exceptions\PageNotFoundException;

class ContactInfo extends BaseController
{
    protected $contactInfoModel;

    public function __construct()
    {
        $this->contactInfoModel = new ContactInfoModel();
    }

    public function index()
    {
        $data = [
            'title' => 'Contact Information',
            'info'  => $this->contactInfoModel->getInfo(),
        ];

        return view('admin/contact_info/index', $data);
    }

    public function edit()
    {
        $info = $this->contactInfoModel->getInfo();

        $data = [
            'title' => 'Edit Contact Information',
            'info'  => $info,
        ];

        return view('admin/contact_info/edit', $data);
    }

    public function update()
    {
        $info = $this->contactInfoModel->getInfo();
        $id = $info['id'];

        $rules = $this->contactInfoModel->validationRules;

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'email'    => $this->request->getPost('email'),
            'phone'    => $this->request->getPost('phone'),
            'address'  => $this->request->getPost('address'),
            'whatsapp' => $this->request->getPost('whatsapp'),
            'map_url'  => $this->request->getPost('map_url'),
        ];

        $this->contactInfoModel->update($id, $data);

        return redirect()->to('contact-info')->with('message', 'Contact information updated successfully.');
    }
}
