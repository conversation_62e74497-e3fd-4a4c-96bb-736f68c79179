<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class GallerySeeder extends Seeder
{
    public function run()
    {
        $data = [
            [
                'title'       => 'Project 1',
                'description' => 'This is a description of project 1.',
                'category'    => 'Web Design',
                'status'      => 'active',
                'created_at'  => date('Y-m-d H:i:s'),
                'updated_at'  => date('Y-m-d H:i:s'),
            ],
            [
                'title'       => 'Project 2',
                'description' => 'This is a description of project 2.',
                'category'    => 'Mobile App',
                'status'      => 'active',
                'created_at'  => date('Y-m-d H:i:s'),
                'updated_at'  => date('Y-m-d H:i:s'),
            ],
            [
                'title'       => 'Project 3',
                'description' => 'This is a description of project 3.',
                'category'    => 'Branding',
                'status'      => 'active',
                'created_at'  => date('Y-m-d H:i:s'),
                'updated_at'  => date('Y-m-d H:i:s'),
            ],
        ];

        $this->db->table('gallery')->insertBatch($data);
        
        echo "Gallery items seeded successfully.\n";
    }
}
