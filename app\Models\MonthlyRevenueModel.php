<?php

namespace App\Models;

use CodeIgniter\Model;

class MonthlyRevenueModel extends Model
{
    protected $table            = 'monthly_revenue';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'year', 'month', 'total_revenue', 
        'draft_count', 'draft_amount',
        'sent_count', 'sent_amount',
        'paid_count', 'paid_amount',
        'cancelled_count', 'cancelled_amount',
        'created_at', 'updated_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    /**
     * Get monthly revenue for a specific year and month
     *
     * @param int $year
     * @param int $month
     * @return array|null
     */
    public function getMonthlyRevenue($year, $month)
    {
        return $this->where('year', $year)
                    ->where('month', $month)
                    ->first();
    }

    /**
     * Get monthly revenue for the last N months
     *
     * @param int $months Number of months to retrieve
     * @return array
     */
    public function getLastMonths($months = 6)
    {
        return $this->orderBy('year DESC, month DESC')
                    ->limit($months)
                    ->find();
    }

    /**
     * Save or update monthly revenue data
     *
     * @param int $year
     * @param int $month
     * @param array $data Revenue data to save
     * @return bool
     */
    public function saveMonthlyRevenue($year, $month, $data)
    {
        $existing = $this->getMonthlyRevenue($year, $month);
        
        if ($existing) {
            return $this->update($existing['id'], $data);
        } else {
            $data['year'] = $year;
            $data['month'] = $month;
            return $this->insert($data);
        }
    }

    /**
     * Calculate and save the current month's revenue
     *
     * @return bool
     */
    public function calculateCurrentMonthRevenue()
    {
        $invoiceModel = new InvoiceModel();
        $year = date('Y');
        $month = date('m');
        
        // Get start and end dates for current month
        $startDate = "$year-$month-01";
        $endDate = date('Y-m-t', strtotime($startDate));
        
        // Get invoice statistics for the current month
        $stats = $invoiceModel->getInvoiceStatusStats($startDate, $endDate);
        
        // Prepare data for saving
        $data = [
            'total_revenue' => $stats['paid']['amount'],
            'draft_count' => $stats['draft']['count'],
            'draft_amount' => $stats['draft']['amount'],
            'sent_count' => $stats['sent']['count'],
            'sent_amount' => $stats['sent']['amount'],
            'paid_count' => $stats['paid']['count'],
            'paid_amount' => $stats['paid']['amount'],
            'cancelled_count' => $stats['cancelled']['count'],
            'cancelled_amount' => $stats['cancelled']['amount'],
        ];
        
        return $this->saveMonthlyRevenue($year, $month, $data);
    }

    /**
     * Get yearly revenue data grouped by month
     *
     * @param int $year
     * @return array
     */
    public function getYearlyRevenue($year)
    {
        return $this->where('year', $year)
                    ->orderBy('month', 'ASC')
                    ->findAll();
    }
}
