<?php

namespace App\Models;

use CodeIgniter\Model;

class BlogCommentModel extends Model
{
    protected $table            = 'blog_comments';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['post_id', 'name', 'email', 'website', 'comment', 'status'];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules      = [
        'post_id' => 'required|integer',
        'name'    => 'required|min_length[3]|max_length[100]',
        'email'   => 'required|valid_email|max_length[100]',
        'website' => 'permit_empty|valid_url_strict|max_length[255]',
        'comment' => 'required|min_length[5]',
        'status'  => 'required|in_list[pending,approved,spam]',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Get comments for a post
     *
     * @param int $postId
     * @param string $status
     * @return array
     */
    public function getCommentsByPostId($postId, $status = 'approved')
    {
        return $this->where('post_id', $postId)
                    ->where('status', $status)
                    ->orderBy('created_at', 'ASC')
                    ->findAll();
    }

    /**
     * Count comments by status
     *
     * @param string $status
     * @return int
     */
    public function countCommentsByStatus($status = null)
    {
        if ($status) {
            return $this->where('status', $status)->countAllResults();
        }
        
        return $this->countAllResults();
    }

    /**
     * Get recent comments
     *
     * @param int $limit
     * @param string $status
     * @return array
     */
    public function getRecentComments($limit = 5, $status = 'approved')
    {
        $builder = $this->db->table('blog_comments bc');
        $builder->select('bc.*, bp.title as post_title, bp.slug as post_slug');
        $builder->join('blog_posts bp', 'bp.id = bc.post_id');
        $builder->where('bc.status', $status);
        $builder->orderBy('bc.created_at', 'DESC');
        $builder->limit($limit);
        
        return $builder->get()->getResultArray();
    }
}
