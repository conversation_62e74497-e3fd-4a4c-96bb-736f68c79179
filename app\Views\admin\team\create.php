<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-end mb-4">
    <a href="<?= site_url('team') ?>" class="btn btn-secondary">Back to Team</a>
</div>

<div class="card">
    <div class="card-body">
        <?php if (session()->has('errors')): ?>
            <div class="alert alert-danger">
                <ul>
                    <?php foreach (session('errors') as $error): ?>
                        <li><?= $error ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <form action="<?= site_url('team/create') ?>" method="post" enctype="multipart/form-data">
            <div class="mb-3">
                <label for="name" class="form-label">Name</label>
                <input type="text" class="form-control" id="name" name="name" value="<?= old('name') ?>" required>
            </div>
            
            <div class="mb-3">
                <label for="position" class="form-label">Position</label>
                <input type="text" class="form-control" id="position" name="position" value="<?= old('position') ?>" required>
            </div>
            
            <div class="mb-3">
                <label for="bio" class="form-label">Bio</label>
                <textarea class="form-control" id="bio" name="bio" rows="4"><?= old('bio') ?></textarea>
            </div>
            
            <div class="mb-3">
                <label for="image" class="form-label">Image</label>
                <input type="file" class="form-control" id="image" name="image">
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="facebook" class="form-label">Facebook URL</label>
                    <input type="url" class="form-control" id="facebook" name="facebook" value="<?= old('facebook') ?>">
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="twitter" class="form-label">Twitter URL</label>
                    <input type="url" class="form-control" id="twitter" name="twitter" value="<?= old('twitter') ?>">
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="linkedin" class="form-label">LinkedIn URL</label>
                    <input type="url" class="form-control" id="linkedin" name="linkedin" value="<?= old('linkedin') ?>">
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="instagram" class="form-label">Instagram URL</label>
                    <input type="url" class="form-control" id="instagram" name="instagram" value="<?= old('instagram') ?>">
                </div>
            </div>
            
            <div class="mb-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status" required>
                    <option value="active" <?= old('status') === 'active' ? 'selected' : '' ?>>Active</option>
                    <option value="inactive" <?= old('status') === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                </select>
            </div>
            
            <div class="d-grid">
                <button type="submit" class="btn btn-primary">Save Team Member</button>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?>
