<?php

namespace App\Models;

use CodeIgniter\Model;

class InvoiceModel extends Model
{
    protected $table            = 'invoices';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'invoice_number', 'client_id', 'invoice_date', 'due_date', 'subject',
        'subtotal', 'tax', 'total', 'status', 'notes', 'upi_id', 'qr_code',
        'created_at', 'updated_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules      = [
        'invoice_number' => 'required|max_length[50]|is_unique[invoices.invoice_number]',
        'client_id'      => 'required|integer',
        'invoice_date'   => 'required|valid_date',
        'subject'        => 'required|max_length[255]',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    /**
     * Generate a unique invoice number
     *
     * @return string
     */
    public function generateInvoiceNumber()
    {
        $prefix = 'INV-' . date('Ym') . '-';
        $lastInvoice = $this->select('invoice_number')
                            ->like('invoice_number', $prefix, 'after')
                            ->orderBy('id', 'DESC')
                            ->first();

        if ($lastInvoice) {
            // Extract the numeric part and increment
            $lastNumber = (int) substr($lastInvoice['invoice_number'], strlen($prefix));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        // Format with leading zeros (3 digits)
        return $prefix . str_pad($newNumber, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Get invoice with client details
     *
     * @param int $id
     * @return array|null
     */
    public function getInvoiceWithDetails($id)
    {
        $builder = $this->db->table('invoices i');
        $builder->select('i.*, c.name as client_name, c.phone as client_phone, c.email as client_email, c.address as client_address');
        $builder->join('clients c', 'c.id = i.client_id');
        $builder->where('i.id', $id);

        $query = $builder->get();
        return $query->getRowArray();
    }

    /**
     * Get all invoices with client names
     *
     * @return array
     */
    public function getAllInvoicesWithClients()
    {
        $builder = $this->db->table('invoices i');
        $builder->select('i.*, c.name as client_name');
        $builder->join('clients c', 'c.id = i.client_id');
        $builder->orderBy('i.id', 'DESC');

        $query = $builder->get();
        return $query->getResultArray();
    }

    /**
     * Get paginated invoices with client names
     *
     * @param int $limit
     * @param int $offset
     * @return array
     */
    public function getAllInvoicesWithClientsPaginated($limit = 10, $offset = 0)
    {
        $builder = $this->db->table('invoices i');
        $builder->select('i.*, c.name as client_name');
        $builder->join('clients c', 'c.id = i.client_id');
        $builder->orderBy('i.id', 'DESC');
        $builder->limit($limit, $offset);

        $query = $builder->get();
        return $query->getResultArray();
    }

    /**
     * Get invoice status statistics with amounts
     *
     * @param string|null $startDate Optional start date (YYYY-MM-DD)
     * @param string|null $endDate Optional end date (YYYY-MM-DD)
     * @return array
     */
    public function getInvoiceStatusStats($startDate = null, $endDate = null)
    {
        // Initialize stats with default values
        $stats = [
            'draft' => [
                'count' => 0,
                'amount' => 0,
                'percentage' => 0,
                'icon' => 'file-earmark',
                'color' => 'secondary',
                'label' => 'Draft',
                'description' => 'Not yet sent'
            ],
            'sent' => [
                'count' => 0,
                'amount' => 0,
                'percentage' => 0,
                'icon' => 'envelope',
                'color' => 'info',
                'label' => 'Sent',
                'description' => 'Sent to client'
            ],
            'paid' => [
                'count' => 0,
                'amount' => 0,
                'percentage' => 0,
                'icon' => 'check-circle',
                'color' => 'success',
                'label' => 'Paid',
                'description' => 'Payment received'
            ],
            'cancelled' => [
                'count' => 0,
                'amount' => 0,
                'percentage' => 0,
                'icon' => 'x-circle',
                'color' => 'danger',
                'label' => 'Cancelled',
                'description' => 'Invoice cancelled'
            ]
        ];

        // Start with a base query
        $baseQuery = $this;

        // Apply date filters if provided
        if ($startDate && $endDate) {
            $baseQuery = $baseQuery->where('invoice_date >=', $startDate)
                                  ->where('invoice_date <=', $endDate);
        }

        // Get total count
        $totalCount = $baseQuery->countAllResults(false); // false to not reset the query builder

        // If there are no invoices, return the default stats
        if ($totalCount === 0) {
            $stats['total'] = [
                'count' => 0,
                'amount' => 0
            ];
            return $stats;
        }

        // Get counts and amounts for each status
        foreach (array_keys($stats) as $status) {
            // Clone the base query for this status
            $statusQuery = clone $baseQuery;

            // Get count for this status
            $statusCount = $statusQuery->where('status', $status)->countAllResults(false);
            $stats[$status]['count'] = $statusCount;

            // Get total amount for this status
            $statusAmount = 0;
            $amountQuery = clone $baseQuery;
            $result = $amountQuery->selectSum('total')->where('status', $status)->first();
            if ($result && isset($result['total'])) {
                $statusAmount = (float)$result['total'];
            }
            $stats[$status]['amount'] = $statusAmount;

            // Calculate percentage of total
            if ($totalCount > 0) {
                $stats[$status]['percentage'] = round(($statusCount / $totalCount) * 100);
            }
        }

        // Add total stats
        $totalAmount = 0;
        foreach (['draft', 'sent', 'paid', 'cancelled'] as $status) {
            $totalAmount += $stats[$status]['amount'];
        }

        $stats['total'] = [
            'count' => $totalCount,
            'amount' => $totalAmount
        ];

        return $stats;
    }
}
