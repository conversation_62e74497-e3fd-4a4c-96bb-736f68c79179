<?php

namespace App\Controllers;

use App\Models\UserModel;
use CodeIgniter\Exceptions\PageNotFoundException;

class Profile extends BaseController
{
    protected $userModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
    }

    public function index()
    {
        $userId = session()->get('id');
        $user = $this->userModel->find($userId);

        if (!$user) {
            throw new PageNotFoundException('User not found');
        }

        $data = [
            'title' => 'My Profile',
            'user'  => $user,
        ];

        return view('admin/profile/index', $data);
    }

    public function edit()
    {
        $userId = session()->get('id');
        $user = $this->userModel->find($userId);

        if (!$user) {
            throw new PageNotFoundException('User not found');
        }

        $data = [
            'title' => 'Edit Profile',
            'user'  => $user,
        ];

        return view('admin/profile/edit', $data);
    }

    public function update()
    {
        $userId = session()->get('id');
        $user = $this->userModel->find($userId);

        if (!$user) {
            throw new PageNotFoundException('User not found');
        }

        // Log the request data for debugging
        log_message('debug', 'Profile update request: ' . json_encode($this->request->getPost()));
        log_message('debug', 'Profile update files: ' . json_encode($this->request->getFileMultiple('profile_image')));

        $rules = [
            'name'  => 'required|min_length[3]|max_length[100]',
            'email' => 'required|valid_email|is_unique[users.email,id,' . $userId . ']',
            'phone' => 'permit_empty|max_length[20]',
            'bio'   => 'permit_empty',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Handle profile image upload
        $profileImage = $this->request->getFile('profile_image');
        $imageName = $user['profile_image'];

        log_message('debug', 'Profile image object: ' . json_encode([
            'name' => $profileImage->getName(),
            'type' => $profileImage->getClientMimeType(),
            'size' => $profileImage->getSize(),
            'error' => $profileImage->getError(),
            'isValid' => $profileImage->isValid(),
            'hasMoved' => $profileImage->hasMoved()
        ]));

        if ($profileImage->getSize() > 0 && $profileImage->isValid() && !$profileImage->hasMoved()) {
            // Delete old image if exists
            if ($user['profile_image'] && file_exists(ROOTPATH . 'public/uploads/profile/' . $user['profile_image'])) {
                unlink(ROOTPATH . 'public/uploads/profile/' . $user['profile_image']);
                log_message('debug', 'Deleted old profile image: ' . $user['profile_image']);
            }

            $imageName = $profileImage->getRandomName();
            log_message('debug', 'New image name: ' . $imageName);

            // Create directory if it doesn't exist
            $uploadPath = ROOTPATH . 'public/uploads/profile';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0777, true);
                log_message('debug', 'Created directory: ' . $uploadPath);
            }

            // Move the file to the uploads directory
            try {
                if ($profileImage->move($uploadPath, $imageName)) {
                    // Log success
                    log_message('info', 'Profile image uploaded successfully: ' . $imageName);

                    // Verify the file was created
                    $fullPath = $uploadPath . '/' . $imageName;
                    log_message('debug', 'File exists after upload: ' . (file_exists($fullPath) ? 'Yes' : 'No'));
                    log_message('debug', 'File permissions: ' . substr(sprintf('%o', fileperms($fullPath)), -4));
                } else {
                    // Log error
                    log_message('error', 'Failed to upload profile image: ' . $profileImage->getErrorString());
                    $imageName = $user['profile_image']; // Keep the old image name if upload fails
                }
            } catch (\Exception $e) {
                log_message('error', 'Exception during file upload: ' . $e->getMessage());
                $imageName = $user['profile_image'];
            }
        } else {
            log_message('debug', 'No valid profile image to upload');
        }

        $data = [
            'name'          => $this->request->getPost('name'),
            'email'         => $this->request->getPost('email'),
            'phone'         => $this->request->getPost('phone'),
            'address'       => $this->request->getPost('address'),
            'bio'           => $this->request->getPost('bio'),
            'profile_image' => $imageName,
        ];

        // Log the data being sent to the database
        log_message('debug', 'Updating user with data: ' . json_encode($data));

        // Use a direct database query to update the profile
        try {
            // Connect to the database directly
            $db = \Config\Database::connect();

            // Update only the profile image first to isolate any issues
            $imageData = ['profile_image' => $imageName];
            $builder = $db->table('users');
            $builder->where('id', $userId);
            $imageResult = $builder->update($imageData);
            log_message('debug', 'Profile image update result: ' . ($imageResult ? 'Success' : 'Failed'));

            // Now update the rest of the user data
            $otherData = [
                'name'    => $this->request->getPost('name'),
                'email'   => $this->request->getPost('email'),
                'phone'   => $this->request->getPost('phone'),
                'address' => $this->request->getPost('address'),
                'bio'     => $this->request->getPost('bio'),
            ];
            $builder->where('id', $userId);
            $dataResult = $builder->update($otherData);
            log_message('debug', 'Other data update result: ' . ($dataResult ? 'Success' : 'Failed'));

            // Double-check the user data after update
            $updatedUser = $this->userModel->find($userId);
            log_message('debug', 'User after update: ' . json_encode($updatedUser));

            // Also check directly from the database
            $directUser = $db->table('users')->where('id', $userId)->get()->getRowArray();
            log_message('debug', 'User from direct query: ' . json_encode($directUser));
        } catch (\Exception $e) {
            log_message('error', 'Exception during database update: ' . $e->getMessage());
        }

        // Update session data
        session()->set('name', $data['name']);
        session()->set('email', $data['email']);

        return redirect()->to('profile')->with('message', 'Profile updated successfully');
    }

    public function changePassword()
    {
        $data = [
            'title' => 'Change Password',
        ];

        return view('admin/profile/change_password', $data);
    }

    public function updatePassword()
    {
        $userId = session()->get('id');
        $user = $this->userModel->find($userId);

        if (!$user) {
            throw new PageNotFoundException('User not found');
        }

        $rules = [
            'current_password' => 'required',
            'new_password'     => 'required|min_length[6]',
            'confirm_password' => 'required|matches[new_password]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Verify current password
        if (!password_verify($this->request->getPost('current_password'), $user['password'])) {
            return redirect()->back()->with('error', 'Current password is incorrect');
        }

        $data = [
            'password' => $this->request->getPost('new_password'),
        ];

        $this->userModel->update($userId, $data);

        return redirect()->to('profile')->with('message', 'Password changed successfully');
    }
}
