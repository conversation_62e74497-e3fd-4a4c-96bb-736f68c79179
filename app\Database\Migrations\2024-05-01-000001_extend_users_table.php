<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class ExtendUsersTable extends Migration
{
    public function up()
    {
        $this->forge->addColumn('users', [
            'profile_image' => [
                'type'       => 'VARCHAR',
                'constraint' => '255',
                'null'       => true,
                'after'      => 'password',
            ],
            'phone' => [
                'type'       => 'VARCHAR',
                'constraint' => '20',
                'null'       => true,
                'after'      => 'profile_image',
            ],
            'address' => [
                'type'       => 'VARCHAR',
                'constraint' => '255',
                'null'       => true,
                'after'      => 'phone',
            ],
            'bio' => [
                'type' => 'TEXT',
                'null' => true,
                'after' => 'address',
            ],
            'role' => [
                'type'       => 'ENUM',
                'constraint' => ['admin', 'editor', 'user'],
                'default'    => 'admin',
                'after'      => 'bio',
            ],
            'last_login' => [
                'type' => 'DATETIME',
                'null' => true,
                'after' => 'role',
            ],
        ]);
    }

    public function down()
    {
        $this->forge->dropColumn('users', 'profile_image');
        $this->forge->dropColumn('users', 'phone');
        $this->forge->dropColumn('users', 'address');
        $this->forge->dropColumn('users', 'bio');
        $this->forge->dropColumn('users', 'role');
        $this->forge->dropColumn('users', 'last_login');
    }
}
