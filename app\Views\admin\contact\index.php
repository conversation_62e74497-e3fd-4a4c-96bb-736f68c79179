<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>


<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Subject</th>
                        <th>Status</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($messages)): ?>
                        <tr>
                            <td colspan="7" class="text-center">No messages found</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($messages as $message): ?>
                            <tr>
                                <td><?= $message['id'] ?></td>
                                <td><?= $message['name'] ?></td>
                                <td><?= $message['email'] ?></td>
                                <td><?= $message['subject'] ?></td>
                                <td>
                                    <span class="badge bg-<?= $message['status'] === 'unread' ? 'danger' : 'success' ?>">
                                        <?= ucfirst($message['status']) ?>
                                    </span>
                                </td>
                                <td><?= date('M d, Y', strtotime($message['created_at'])) ?></td>
                                <td>
                                    <a href="<?= site_url('contact/view/' . $message['id']) ?>" class="btn btn-sm btn-primary">View</a>
                                    <a href="<?= site_url('contact/delete/' . $message['id']) ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this message?')">Delete</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <?php if (!empty($pager)) : ?>
            <?= $pager->links('messages', 'admin_full') ?>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>
