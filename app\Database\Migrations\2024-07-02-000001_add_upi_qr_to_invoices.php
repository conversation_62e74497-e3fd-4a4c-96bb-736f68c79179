<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddUpiQrToInvoices extends Migration
{
    public function up()
    {
        $fields = [
            'upi_id' => [
                'type'       => 'VARCHAR',
                'constraint' => '100',
                'null'       => true,
                'after'      => 'ifsc_code'
            ],
            'qr_code' => [
                'type'       => 'VARCHAR',
                'constraint' => '255',
                'null'       => true,
                'after'      => 'upi_id'
            ],
        ];
        
        $this->forge->addColumn('invoices', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('invoices', 'upi_id');
        $this->forge->dropColumn('invoices', 'qr_code');
    }
}
