<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-end mb-4">
    <a href="<?= site_url('gallery/new') ?>" class="btn btn-primary">Add New Gallery Item</a>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Image</th>
                        <th>Status</th>
                        <th>Created At</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($gallery)): ?>
                        <tr>
                            <td colspan="5" class="text-center">No gallery items found</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($gallery as $item): ?>
                            <tr>
                                <td><?= $item['id'] ?></td>
                                <td>
                                    <?php if ($item['image']): ?>
                                        <img src="<?= base_url('public/uploads/gallery/' . $item['image']) ?>" alt="Gallery Image" width="50">
                                    <?php else: ?>
                                        No Image
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $item['status'] === 'active' ? 'success' : 'danger' ?>">
                                        <?= ucfirst($item['status']) ?>
                                    </span>
                                </td>
                                <td><?= date('M d, Y', strtotime($item['created_at'])) ?></td>
                                <td>
                                    <a href="<?= site_url('gallery/edit/' . $item['id']) ?>" class="btn btn-sm btn-primary">Edit</a>
                                    <a href="<?= site_url('gallery/delete/' . $item['id']) ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this gallery item?')">Delete</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <?php if (!empty($pager)) : ?>
            <?= $pager->links('gallery', 'admin_full') ?>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>
