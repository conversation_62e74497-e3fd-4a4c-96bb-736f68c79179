<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-end align-items-center mb-4">
    <a href="<?= site_url('clients/new') ?>" class="btn btn-primary">
        <i class="bi bi-plus-circle"></i> Add New Client
    </a>
</div>


<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Client List</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Phone</th>
                        <th>Email</th>
                        <th>Address</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($clients)): ?>
                        <tr>
                            <td colspan="6" class="text-center">No clients found</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($clients as $client): ?>
                            <tr>
                                <td><?= $client['id'] ?></td>
                                <td><?= $client['name'] ?></td>
                                <td><?= $client['phone'] ?? 'N/A' ?></td>
                                <td><?= $client['email'] ?? 'N/A' ?></td>
                                <td><?= $client['address'] ?? 'N/A' ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= site_url('clients/edit/' . $client['id']) ?>" class="btn btn-sm btn-primary">
                                            <i class="bi bi-pencil"></i> Edit
                                        </a>
                                        <a href="<?= site_url('clients/delete/' . $client['id']) ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this client?')">
                                            <i class="bi bi-trash"></i> Delete
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <?php if (!empty($pager)) : ?>
            <?= $pager->links('clients', 'admin_full') ?>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>
