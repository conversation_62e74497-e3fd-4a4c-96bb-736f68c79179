<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-end align-items-center mb-4">
    <a href="<?= site_url('contact-info') ?>" class="btn btn-secondary">
        <i class="bi bi-arrow-left"></i> Back to Contact Information
    </a>
</div>

<?php if (session()->has('errors')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <h4 class="alert-heading">Errors</h4>
        <ul class="mb-0">
            <?php foreach (session('errors') as $error): ?>
                <li><?= $error ?></li>
            <?php endforeach; ?>
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Edit Contact Information</h6>
    </div>
    <div class="card-body">
        <form action="<?= site_url('contact-info/update') ?>" method="post">
            <div class="mb-3">
                <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                <input type="email" class="form-control" id="email" name="email" value="<?= old('email', $info['email']) ?>" required>
                <div class="form-text">This email will be displayed on the website and used for contact form notifications.</div>
            </div>
            
            <div class="mb-3">
                <label for="phone" class="form-label">Phone <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="phone" name="phone" value="<?= old('phone', $info['phone']) ?>" required>
            </div>
            
            <div class="mb-3">
                <label for="address" class="form-label">Address <span class="text-danger">*</span></label>
                <textarea class="form-control" id="address" name="address" rows="3" required><?= old('address', $info['address']) ?></textarea>
            </div>
            
            <div class="mb-3">
                <label for="whatsapp" class="form-label">WhatsApp Link</label>
                <input type="text" class="form-control" id="whatsapp" name="whatsapp" value="<?= old('whatsapp', $info['whatsapp']) ?>">
                <div class="form-text">Format: https://wa.me/9451732775 (include country code without + symbol)</div>
            </div>
            
            <div class="mb-3">
                <label for="map_url" class="form-label">Google Maps URL</label>
                <input type="text" class="form-control" id="map_url" name="map_url" value="<?= old('map_url', $info['map_url']) ?>">
                <div class="form-text">
                    Get this from Google Maps by searching for your address, clicking "Share", and selecting "Embed a map" tab.
                    Copy the URL from the iframe src attribute.
                </div>
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-save"></i> Save Changes
                </button>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?>
