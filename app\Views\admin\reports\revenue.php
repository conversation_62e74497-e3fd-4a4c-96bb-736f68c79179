<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div id="lastUpdatedInfo" class="text-muted small">
            <i class="bi bi-clock-history me-1"></i> Last updated: <?= date('M d, Y h:i A', strtotime($lastUpdated)) ?>
        </div>
        <div class="d-flex gap-2">
            <form action="" method="get" class="d-flex align-items-center">
                <select name="year" class="form-select form-select-sm me-2" onchange="this.form.submit()">
                    <?php foreach ($years as $year): ?>
                        <option value="<?= $year ?>" <?= $selectedYear == $year ? 'selected' : '' ?>><?= $year ?></option>
                    <?php endforeach; ?>
                </select>
            </form>
            <button id="refreshButton" class="btn btn-sm btn-primary" <?= ($selectedYear != $currentYear) ? 'disabled' : '' ?>>
                <i class="bi bi-arrow-clockwise me-1"></i> Refresh Data
            </button>
        </div>
    </div>

    <div id="alertContainer">
        <?php if (session()->getFlashdata('message')): ?>
            <div class="alert alert-success">
                <?= session()->getFlashdata('message') ?>
            </div>
        <?php endif; ?>

        <?php if (session()->getFlashdata('error')): ?>
            <div class="alert alert-danger">
                <?= session()->getFlashdata('error') ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Yearly Revenue Chart -->
    <div class="card mb-4 border-0 shadow-sm">
        <div class="card-header bg-transparent">
            <h5 class="mb-0">Yearly Revenue Overview (<?= $selectedYear ?>)</h5>
        </div>
        <div class="card-body">
            <div style="height: 400px;">
                <canvas id="yearlyRevenueChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Monthly Revenue Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-transparent d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Monthly Revenue Breakdown</h5>
            <div id="tableLoadingIndicator" class="spinner-border spinner-border-sm text-primary d-none" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Month</th>
                            <th>Total Revenue</th>
                            <th>Paid Invoices</th>
                            <th>Sent Invoices</th>
                            <th>Draft Invoices</th>
                            <th>Cancelled Invoices</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="revenueTableBody">
                        <?php
                        $monthNames = [
                            1 => 'January', 2 => 'February', 3 => 'March', 4 => 'April',
                            5 => 'May', 6 => 'June', 7 => 'July', 8 => 'August',
                            9 => 'September', 10 => 'October', 11 => 'November', 12 => 'December'
                        ];

                        // Create an array with all months
                        $allMonths = [];
                        foreach ($monthNames as $num => $name) {
                            $allMonths[$num] = [
                                'month' => $num,
                                'month_name' => $name,
                                'total_revenue' => 0,
                                'paid_count' => 0,
                                'paid_amount' => 0,
                                'sent_count' => 0,
                                'sent_amount' => 0,
                                'draft_count' => 0,
                                'draft_amount' => 0,
                                'cancelled_count' => 0,
                                'cancelled_amount' => 0,
                                'has_data' => false
                            ];
                        }

                        // Fill in actual data
                        foreach ($monthlyData as $data) {
                            $monthNum = (int)$data['month'];
                            $allMonths[$monthNum] = [
                                'month' => $monthNum,
                                'month_name' => $monthNames[$monthNum],
                                'total_revenue' => $data['total_revenue'],
                                'paid_count' => $data['paid_count'],
                                'paid_amount' => $data['paid_amount'],
                                'sent_count' => $data['sent_count'],
                                'sent_amount' => $data['sent_amount'],
                                'draft_count' => $data['draft_count'],
                                'draft_amount' => $data['draft_amount'],
                                'cancelled_count' => $data['cancelled_count'],
                                'cancelled_amount' => $data['cancelled_amount'],
                                'has_data' => true
                            ];
                        }

                        // Sort by month (descending)
                        krsort($allMonths);

                        foreach ($allMonths as $monthData):
                            $monthNum = $monthData['month'];
                        ?>
                            <tr>
                                <td><?= $monthData['month_name'] ?></td>
                                <td>₹<?= number_format($monthData['total_revenue'], 2) ?></td>
                                <td>
                                    <span class="badge bg-success-subtle text-success"><?= $monthData['paid_count'] ?></span>
                                    <span class="ms-2">₹<?= number_format($monthData['paid_amount'], 2) ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-info-subtle text-info"><?= $monthData['sent_count'] ?></span>
                                    <span class="ms-2">₹<?= number_format($monthData['sent_amount'], 2) ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary-subtle text-secondary"><?= $monthData['draft_count'] ?></span>
                                    <span class="ms-2">₹<?= number_format($monthData['draft_amount'], 2) ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-danger-subtle text-danger"><?= $monthData['cancelled_count'] ?></span>
                                    <span class="ms-2">₹<?= number_format($monthData['cancelled_amount'], 2) ?></span>
                                </td>
                                <td>
                                    <a href="<?= site_url("revenue-reports/monthly/{$selectedYear}/{$monthNum}") ?>" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i> Details
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Yearly Revenue Chart
    const ctx = document.getElementById('yearlyRevenueChart').getContext('2d');
    let revenueChart;

    function initializeChart(chartData) {
        revenueChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: chartData.months,
                datasets: [
                    {
                        label: 'Paid',
                        data: chartData.paidAmount,
                        backgroundColor: 'rgba(16, 185, 129, 0.8)',
                        borderColor: 'rgba(16, 185, 129, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Sent',
                        data: chartData.sentAmount,
                        backgroundColor: 'rgba(14, 165, 233, 0.8)',
                        borderColor: 'rgba(14, 165, 233, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Draft',
                        data: chartData.draftAmount,
                        backgroundColor: 'rgba(107, 114, 128, 0.8)',
                        borderColor: 'rgba(107, 114, 128, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Cancelled',
                        data: chartData.cancelledAmount,
                        backgroundColor: 'rgba(239, 68, 68, 0.8)',
                        borderColor: 'rgba(239, 68, 68, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        stacked: true,
                    },
                    y: {
                        stacked: true,
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '₹' + value.toLocaleString('en-IN');
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ₹' + context.parsed.y.toLocaleString('en-IN');
                            }
                        }
                    }
                }
            }
        });
    }

    // Initialize chart with initial data
    const initialChartData = <?= json_encode($chartData) ?>;
    initializeChart(initialChartData);

    // Handle refresh button click
    const refreshButton = document.getElementById('refreshButton');
    const lastUpdatedInfo = document.getElementById('lastUpdatedInfo');
    const alertContainer = document.getElementById('alertContainer');
    const tableBody = document.getElementById('revenueTableBody');
    const tableLoadingIndicator = document.getElementById('tableLoadingIndicator');
    const chartContainer = document.getElementById('yearlyRevenueChart').parentElement;

    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            // Show loading state
            refreshButton.disabled = true;
            refreshButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Refreshing...';
            tableLoadingIndicator.classList.remove('d-none');

            // Add loading overlay to chart
            const chartOverlay = document.createElement('div');
            chartOverlay.id = 'chartLoadingOverlay';
            chartOverlay.style.position = 'absolute';
            chartOverlay.style.top = '0';
            chartOverlay.style.left = '0';
            chartOverlay.style.width = '100%';
            chartOverlay.style.height = '100%';
            chartOverlay.style.backgroundColor = 'rgba(255, 255, 255, 0.7)';
            chartOverlay.style.display = 'flex';
            chartOverlay.style.justifyContent = 'center';
            chartOverlay.style.alignItems = 'center';
            chartOverlay.style.zIndex = '10';
            chartOverlay.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>';

            chartContainer.style.position = 'relative';
            chartContainer.appendChild(chartOverlay);

            // Make AJAX request to refresh data
            fetch('<?= site_url('revenue-reports') ?>?refresh=true&year=<?= $currentYear ?>&ajax=true')
                .then(response => response.json())
                .then(data => {
                    // Update last updated time
                    lastUpdatedInfo.innerHTML = '<i class="bi bi-clock-history me-1"></i> Last updated: ' + data.lastUpdated;

                    // Show success message
                    alertContainer.innerHTML = '<div class="alert alert-success alert-dismissible fade show">' +
                        'Revenue data refreshed successfully.' +
                        '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                        '</div>';

                    // Update chart data
                    if (revenueChart) {
                        revenueChart.destroy();
                    }
                    initializeChart(data.chartData);

                    // Update table data
                    if (data.tableHtml) {
                        tableBody.innerHTML = data.tableHtml;
                    }

                    // Reset loading states
                    refreshButton.disabled = false;
                    refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i> Refresh Data';
                    tableLoadingIndicator.classList.add('d-none');

                    // Remove chart loading overlay
                    const chartOverlay = document.getElementById('chartLoadingOverlay');
                    if (chartOverlay) {
                        chartOverlay.remove();
                    }

                    // Auto-dismiss alert after 5 seconds
                    setTimeout(() => {
                        const alert = document.querySelector('.alert');
                        if (alert) {
                            const bsAlert = new bootstrap.Alert(alert);
                            bsAlert.close();
                        }
                    }, 5000);
                })
                .catch(error => {
                    console.error('Error refreshing data:', error);

                    // Show error message
                    alertContainer.innerHTML = '<div class="alert alert-danger alert-dismissible fade show">' +
                        'Failed to refresh revenue data. Please try again.' +
                        '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                        '</div>';

                    // Reset loading states
                    refreshButton.disabled = false;
                    refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i> Refresh Data';
                    tableLoadingIndicator.classList.add('d-none');

                    // Remove chart loading overlay
                    const chartOverlay = document.getElementById('chartLoadingOverlay');
                    if (chartOverlay) {
                        chartOverlay.remove();
                    }
                });
        });
    }
});
</script>

<?= $this->endSection() ?>
