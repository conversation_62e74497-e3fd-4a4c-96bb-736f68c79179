<?php

namespace App\Models;

use CodeIgniter\Model;

class UserModel extends Model
{
    protected $table            = 'users';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'name', 'email', 'password', 'profile_image', 'phone',
        'address', 'bio', 'role', 'last_login', 'created_at', 'updated_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules      = [
        'name'     => 'required|min_length[3]|max_length[100]',
        'email'    => 'required|valid_email|is_unique[users.email,id,{id}]',
        'password' => 'required|min_length[6]',
        // Make profile_image optional
        'profile_image' => 'permit_empty',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $beforeInsert   = ['hashPassword'];
    protected $beforeUpdate   = ['hashPassword'];

    protected function hashPassword(array $data)
    {
        if (! isset($data['data']['password'])) {
            return $data;
        }

        $data['data']['password'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);

        return $data;
    }

    /**
     * Update user profile with direct database query to ensure it works
     */
    public function updateProfile($userId, $data)
    {
        // Log the update operation
        log_message('debug', 'UserModel::updateProfile - Updating user ' . $userId . ' with data: ' . json_encode($data));

        // Use the query builder for a direct update
        $builder = $this->db->table($this->table);
        $builder->where('id', $userId);
        $result = $builder->update($data);

        // Log the result
        log_message('debug', 'UserModel::updateProfile - Update result: ' . ($result ? 'Success' : 'Failed'));

        return $result;
    }
}
