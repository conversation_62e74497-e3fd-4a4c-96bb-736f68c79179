<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class RemoveTitleCategoryFromGallery extends Migration
{
    public function up()
    {
        // Drop the title and category columns from the gallery table
        $this->forge->dropColumn('gallery', 'title');
        $this->forge->dropColumn('gallery', 'category');
    }

    public function down()
    {
        // Add back the title and category columns if needed
        $fields = [
            'title' => [
                'type'       => 'VARCHAR',
                'constraint' => '100',
                'null'       => true,
                'after'      => 'id'
            ],
            'category' => [
                'type'       => 'VARCHAR',
                'constraint' => '50',
                'null'       => true,
                'after'      => 'image'
            ],
        ];
        
        $this->forge->addColumn('gallery', $fields);
    }
}
