<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-end align-items-center mb-4">
    <a href="<?= site_url('about-journey/new') ?>" class="btn btn-primary">Add New Milestone</a>
</div>

<?php if (session()->has('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?= session('success') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Year</th>
                        <th>Title</th>
                        <th>Description</th>
                        <th>Status</th>
                        <th>Created At</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($journeys)): ?>
                        <tr>
                            <td colspan="6" class="text-center">No journey milestones found</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($journeys as $journey): ?>
                            <tr>
                                <td><?= $journey['year'] ?></td>
                                <td><?= $journey['title'] ?></td>
                                <td><?= substr($journey['description'], 0, 100) . (strlen($journey['description']) > 100 ? '...' : '') ?></td>
                                <td>
                                    <span class="badge bg-<?= $journey['status'] === 'active' ? 'success' : 'danger' ?>">
                                        <?= ucfirst($journey['status']) ?>
                                    </span>
                                </td>
                                <td><?= date('M d, Y', strtotime($journey['created_at'])) ?></td>
                                <td>
                                    <a href="<?= site_url('about-journey/edit/' . $journey['id']) ?>" class="btn btn-sm btn-primary">Edit</a>
                                    <a href="<?= site_url('about-journey/delete/' . $journey['id']) ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this milestone?')">Delete</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <?php if (!empty($pager)) : ?>
            <?= $pager->links('journey', 'admin_full') ?>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>
