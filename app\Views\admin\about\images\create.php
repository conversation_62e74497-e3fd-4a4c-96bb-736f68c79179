<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-end mb-4">
    <a href="<?= site_url('about-images') ?>" class="btn btn-secondary">Back to Images</a>
</div>

<div class="card">
    <div class="card-body">
        <?php if (session()->has('errors')): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <h5><i class="bi bi-exclamation-triangle-fill me-2"></i>Error</h5>
                <ul class="mb-0 mt-2">
                    <?php 
                    $errors = session('errors');
                    if (is_array($errors)) {
                        foreach ($errors as $field => $error) {
                            echo "<li>{$error}</li>";
                        }
                    } else {
                        echo "<li>{$errors}</li>";
                    }
                    ?>
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <form action="<?= site_url('about-images/create') ?>" method="post" enctype="multipart/form-data">
            <div class="mb-3">
                <label for="image" class="form-label">Image</label>
                <input type="file" class="form-control" id="image" name="image" required>
                <div class="form-text">Recommended size: 800x600 pixels. Max file size: 2MB</div>
            </div>

            <div class="mb-3">
                <label for="alt" class="form-label">Alt Text</label>
                <input type="text" class="form-control" id="alt" name="alt" value="<?= old('alt') ?>">
                <div class="form-text">Description of the image for accessibility</div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="order" class="form-label">Display Order</label>
                        <input type="number" class="form-control" id="order" name="order" value="<?= old('order', 1) ?>" min="1">
                        <div class="form-text">Lower numbers appear first</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="active" <?= old('status') === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="inactive" <?= old('status') === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="mt-4">
                <button type="submit" class="btn btn-primary">Save Image</button>
                <a href="<?= site_url('about-images') ?>" class="btn btn-outline-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?>
