<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class RemoveDescriptionFromGallery extends Migration
{
    public function up()
    {
        // Drop the description column from the gallery table
        $this->forge->dropColumn('gallery', 'description');
    }

    public function down()
    {
        // Add back the description column if needed
        $fields = [
            'description' => [
                'type' => 'TEXT',
                'null' => true,
                'after' => 'id'
            ],
        ];
        
        $this->forge->addColumn('gallery', $fields);
    }
}
