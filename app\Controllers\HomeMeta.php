<?php

namespace App\Controllers;

use App\Models\HomeMetaModel;
use CodeIgniter\Exceptions\PageNotFoundException;

class HomeMeta extends BaseController
{
    protected $homeMetaModel;

    public function __construct()
    {
        $this->homeMetaModel = new HomeMetaModel();
    }

    public function index()
    {
        $meta = $this->homeMetaModel->first();

        if (!$meta) {
            return redirect()->to('/home-meta/new');
        }

        return redirect()->to('/home-meta/edit/' . $meta['id']);
    }

    public function new()
    {
        // Check if meta already exists
        $meta = $this->homeMetaModel->first();

        if ($meta) {
            return redirect()->to('/home-meta/edit/' . $meta['id']);
        }

        $data = [
            'title' => 'Create Home Meta',
        ];

        return view('admin/home/<USER>/create', $data);
    }

    public function create()
    {
        if (!$this->validate($this->homeMetaModel->validationRules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'title'       => $this->request->getPost('title'),
            'description' => $this->request->getPost('description'),
        ];

        $this->homeMetaModel->insert($data);

        return redirect()->to('/home-meta')->with('success', 'Home meta created successfully');
    }

    public function edit($id = null)
    {
        $meta = $this->homeMetaModel->find($id);

        if (!$meta) {
            throw new PageNotFoundException('Home meta not found');
        }

        $data = [
            'title' => 'Edit Home Meta',
            'meta'  => $meta,
        ];

        return view('admin/home/<USER>/edit', $data);
    }

    public function update($id = null)
    {
        $meta = $this->homeMetaModel->find($id);

        if (!$meta) {
            throw new PageNotFoundException('Home meta not found');
        }

        if (!$this->validate($this->homeMetaModel->validationRules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'title'       => $this->request->getPost('title'),
            'description' => $this->request->getPost('description'),
        ];

        $this->homeMetaModel->update($id, $data);

        return redirect()->to('/home-meta')->with('success', 'Home meta updated successfully');
    }
}
