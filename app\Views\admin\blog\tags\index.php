<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-end align-items-center mb-4">
    <a href="<?= site_url('blog-tags/new') ?>" class="btn btn-primary">
        <i class="bi bi-plus-circle me-1"></i> Add New Tag
    </a>
</div>

<?= $this->include('admin/partials/alerts') ?>

<div class="card">
    <div class="card-body">
        <?php if (empty($tags)): ?>
            <div class="text-center py-5">
                <div class="mb-3">
                    <i class="bi bi-tags fs-1 text-muted"></i>
                </div>
                <h5>No Tags Found</h5>
                <p class="text-muted">Get started by creating a new tag.</p>
                <a href="<?= site_url('blog-tags/new') ?>" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-1"></i> Add New Tag
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead>
                        <tr>
                            <th width="50">#</th>
                            <th>Name</th>
                            <th>Slug</th>
                            <th width="150">Created</th>
                            <th width="120">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($tags as $index => $tag): ?>
                            <tr>
                                <td><?= ($index + 1) + (10 * ($pager->getCurrentPage() - 1)) ?></td>
                                <td><?= esc($tag['name']) ?></td>
                                <td><code><?= esc($tag['slug']) ?></code></td>
                                <td><?= date('M d, Y', strtotime($tag['created_at'])) ?></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="<?= site_url('blog-tags/edit/' . $tag['id']) ?>" class="btn btn-outline-primary">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="<?= site_url('blog-tags/delete/' . $tag['id']) ?>" class="btn btn-outline-danger" onclick="return confirm('Are you sure you want to delete this tag?')">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <div class="mt-4">
                <?= $pager->links('tags', 'admin_full') ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>
