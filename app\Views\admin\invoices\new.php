<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-end align-items-center mb-4">
    <a href="<?= site_url('invoices') ?>" class="btn btn-secondary">
        <i class="bi bi-arrow-left"></i> Back to Invoices
    </a>
</div>

<?php if (session()->has('errors')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <h4 class="alert-heading">Errors</h4>
        <ul class="mb-0">
            <?php foreach (session('errors') as $error): ?>
                <li><?= $error ?></li>
            <?php endforeach; ?>
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>



<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Invoice Information</h6>
    </div>
    <div class="card-body">
        <form action="<?= site_url('invoices/create') ?>" method="post" id="invoiceForm" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="invoice_number" class="form-label">Invoice Number <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="invoice_number" name="invoice_number" value="<?= old('invoice_number', $invoice_number) ?>" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="client_id" class="form-label">Client <span class="text-danger">*</span></label>
                        <select class="form-select" id="client_id" name="client_id" required>
                            <option value="">Select Client</option>
                            <?php foreach ($clients as $client): ?>
                                <option value="<?= $client['id'] ?>" <?= old('client_id') == $client['id'] ? 'selected' : '' ?>>
                                    <?= $client['name'] ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text">
                            <a href="<?= site_url('clients/new') ?>" target="_blank">+ Add New Client</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="invoice_date" class="form-label">Invoice Date <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="invoice_date" name="invoice_date" value="<?= old('invoice_date', date('Y-m-d')) ?>" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="due_date" class="form-label">Due Date</label>
                        <input type="date" class="form-control" id="due_date" name="due_date" value="<?= old('due_date') ?>">
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="subject" class="form-label">Subject <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="subject" name="subject" value="<?= old('subject') ?>" required>
            </div>

            <div class="mb-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="draft" <?= old('status', 'draft') == 'draft' ? 'selected' : '' ?>>Draft</option>
                    <option value="sent" <?= old('status') == 'sent' ? 'selected' : '' ?>>Sent</option>
                    <option value="paid" <?= old('status') == 'paid' ? 'selected' : '' ?>>Paid</option>
                    <option value="cancelled" <?= old('status') == 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                </select>
            </div>

            <h5 class="mt-4 mb-3">Invoice Items</h5>
            <div class="table-responsive mb-3">
                <table class="table table-bordered" id="itemsTable">
                    <thead>
                        <tr>
                            <th>Description</th>
                            <th width="150">Fee (₹)</th>
                            <th width="50">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <input type="text" class="form-control" name="description[]" required>
                            </td>
                            <td>
                                <input type="number" class="form-control fee" name="fee[]" value="0" min="0" step="0.01">
                            </td>
                            <td>
                                <button type="button" class="btn btn-danger btn-sm remove-row" disabled>
                                    <i class="bi bi-trash"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="5">
                                <button type="button" class="btn btn-success btn-sm" id="addRow">
                                    <i class="bi bi-plus-circle"></i> Add Item
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-end"><strong>Total:</strong></td>
                            <td>
                                <input type="number" class="form-control" id="total" value="0" readonly>
                            </td>
                            <td></td>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <h5 class="mt-4 mb-3">Payment Information</h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="upi_id" class="form-label">UPI ID</label>
                        <input type="text" class="form-control" id="upi_id" name="upi_id" value="<?= old('upi_id') ?>" placeholder="example@upi">
                    </div>

                    <div class="mb-3">
                        <label for="qr_code" class="form-label">QR Code Image</label>
                        <input type="file" class="form-control" id="qr_code" name="qr_code" accept="image/*">
                        <div class="form-text">Upload a QR code image for payment (JPG, PNG, or GIF)</div>
                    </div>
                </div>

                <!-- Signature is now permanent and not editable -->
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Signature</label>
                        <div class="mt-2">
                            <img src="<?= base_url('public/assets/signature.png') ?>" alt="Signature" class="img-thumbnail" style="max-width: 150px;">
                            <p class="form-text">Permanent signature</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="notes" class="form-label">Notes</label>
                <textarea class="form-control" id="notes" name="notes" rows="3"><?= old('notes') ?></textarea>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-save"></i> Save Invoice
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add new row
    document.getElementById('addRow').addEventListener('click', function() {
        const tbody = document.querySelector('#itemsTable tbody');
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>
                <input type="text" class="form-control" name="description[]" required>
            </td>
            <td>
                <input type="number" class="form-control fee" name="fee[]" value="0" min="0" step="0.01">
            </td>
            <td>
                <button type="button" class="btn btn-danger btn-sm remove-row">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(tr);

        // Enable all remove buttons if there's more than one row
        if (tbody.querySelectorAll('tr').length > 1) {
            const removeButtons = tbody.querySelectorAll('.remove-row');
            removeButtons.forEach(button => {
                button.disabled = false;
            });
        }

        // Add event listeners to the new row
        addRowEventListeners(tr);
    });

    // Remove row
    document.querySelector('#itemsTable tbody').addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-row') || e.target.parentElement.classList.contains('remove-row')) {
            const button = e.target.classList.contains('remove-row') ? e.target : e.target.parentElement;
            const row = button.closest('tr');
            const tbody = row.parentElement;

            row.remove();
            calculateTotal();

            // Disable remove button if only one row remains
            if (tbody.querySelectorAll('tr').length === 1) {
                const lastRemoveButton = tbody.querySelector('.remove-row');
                if (lastRemoveButton) {
                    lastRemoveButton.disabled = true;
                }
            }
        }
    });

    // Add event listeners to initial row
    const initialRow = document.querySelector('#itemsTable tbody tr');
    addRowEventListeners(initialRow);

    function addRowEventListeners(row) {
        const feeInput = row.querySelector('.fee');

        // Update total when fee changes
        feeInput.addEventListener('input', function() {
            calculateTotal();
        });
    }

    function calculateTotal() {
        const feeInputs = document.querySelectorAll('.fee');
        let total = 0;

        feeInputs.forEach(input => {
            total += parseFloat(input.value) || 0;
        });

        document.getElementById('total').value = total.toFixed(2);
    }
});
</script>

<?= $this->endSection() ?>
