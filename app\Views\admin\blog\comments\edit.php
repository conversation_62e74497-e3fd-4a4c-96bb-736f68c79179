<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-end align-items-center mb-4">
    <a href="<?= site_url('blog-comments') ?>" class="btn btn-secondary">
        <i class="bi bi-arrow-left me-1"></i> Back to Comments
    </a>
</div>

<?= $this->include('admin/partials/alerts') ?>

<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Edit Comment</h5>
            </div>
            <div class="card-body">
                <form action="<?= site_url('blog-comments/update/' . $comment['id']) ?>" method="post">
                    <?= csrf_field() ?>
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" value="<?= old('name', $comment['name']) ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="email" name="email" value="<?= old('email', $comment['email']) ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="website" class="form-label">Website</label>
                        <input type="url" class="form-control" id="website" name="website" value="<?= old('website', $comment['website']) ?>">
                    </div>
                    
                    <div class="mb-3">
                        <label for="comment" class="form-label">Comment <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="comment" name="comment" rows="5" required><?= old('comment', $comment['comment']) ?></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="pending" <?= old('status', $comment['status']) === 'pending' ? 'selected' : '' ?>>Pending</option>
                            <option value="approved" <?= old('status', $comment['status']) === 'approved' ? 'selected' : '' ?>>Approved</option>
                            <option value="spam" <?= old('status', $comment['status']) === 'spam' ? 'selected' : '' ?>>Spam</option>
                        </select>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-save me-1"></i> Update Comment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Comment Information</h5>
            </div>
            <div class="card-body">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <span>ID</span>
                        <span class="badge bg-secondary rounded-pill"><?= $comment['id'] ?></span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <span>Date</span>
                        <span><?= date('M d, Y g:i a', strtotime($comment['created_at'])) ?></span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <span>IP Address</span>
                        <span><?= $_SERVER['REMOTE_ADDR'] ?? 'Unknown' ?></span>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Related Post</h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <?php if (!empty($post['featured_image']) && file_exists(FCPATH . $post['featured_image'])): ?>
                        <div class="me-3">
                            <img src="<?= base_url($post['featured_image']) ?>" alt="<?= esc($post['title']) ?>" class="img-thumbnail" width="80">
                        </div>
                    <?php endif; ?>
                    <div>
                        <h6 class="mb-1"><?= esc($post['title']) ?></h6>
                        <p class="card-text text-muted mb-0">
                            <small>
                                <i class="bi bi-calendar me-1"></i> <?= date('M d, Y', strtotime($post['created_at'])) ?>
                            </small>
                        </p>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="<?= site_url('blog-posts/edit/' . $post['id']) ?>" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-pencil me-1"></i> Edit Post
                    </a>
                    <a href="<?= site_url('blog-posts/comments/' . $post['id']) ?>" class="btn btn-sm btn-outline-info">
                        <i class="bi bi-chat-dots me-1"></i> View All Comments
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
