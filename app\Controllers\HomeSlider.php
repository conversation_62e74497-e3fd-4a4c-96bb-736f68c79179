<?php

namespace App\Controllers;

use App\Models\HomeSliderModel;
use CodeIgniter\Exceptions\PageNotFoundException;

class HomeSlider extends BaseController
{
    protected $homeSliderModel;

    public function __construct()
    {
        $this->homeSliderModel = new HomeSliderModel();
    }

    public function index()
    {
        $data = [
            'title'   => 'Manage Home Slider',
            'sliders' => $this->homeSliderModel->orderBy('order', 'ASC')->paginate(10, 'home_slider'),
            'pager'   => $this->homeSliderModel->pager,
        ];

        return view('admin/home/<USER>/index', $data);
    }

    public function new()
    {
        $data = [
            'title' => 'Add New Slider Item',
        ];

        return view('admin/home/<USER>/create', $data);
    }

    public function create()
    {
        // Check validation
        if (!$this->validate([
            'message' => 'required|min_length[3]|max_length[200]',
            'status'  => 'required|in_list[active,inactive]',
            'image'   => 'uploaded[image]|is_image[image]|max_size[image,2048]',
        ])) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        try {
            // Handle file upload
            $image = $this->request->getFile('image');

            // Make sure the uploads directory exists
            $uploadPath = ROOTPATH . 'public/uploads/slider';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0777, true);
            }

            // Generate a new random name for the image
            $imageName = $image->getRandomName();

            // Move the uploaded file to the destination
            $image->move($uploadPath, $imageName);

            if (!$image->hasMoved()) {
                return redirect()->back()->withInput()->with('errors', ['image' => 'Failed to save the image: ' . $image->getErrorString()]);
            }

            // Prepare data for insertion
            $data = [
                'message'   => $this->request->getPost('message'),
                'paragraph' => $this->request->getPost('paragraph'),
                'image'     => $imageName,
                'order'     => $this->request->getPost('order') ?? 1,
                'status'    => $this->request->getPost('status') ?? 'active',
            ];

            // Insert data
            $this->homeSliderModel->insert($data);

            return redirect()->to('/home-slider')->with('success', 'Slider item added successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error creating slider: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('errors', ['error' => 'An error occurred: ' . $e->getMessage()]);
        }
    }

    public function edit($id = null)
    {
        $slider = $this->homeSliderModel->find($id);

        if (!$slider) {
            throw new PageNotFoundException('Slider item not found');
        }

        $data = [
            'title'  => 'Edit Slider Item',
            'slider' => $slider,
        ];

        return view('admin/home/<USER>/edit', $data);
    }

    public function update($id = null)
    {
        $slider = $this->homeSliderModel->find($id);

        if (!$slider) {
            throw new PageNotFoundException('Slider item not found');
        }

        // Validation rules for update - image is optional
        if (!$this->validate([
            'message' => 'required|min_length[3]|max_length[200]',
            'status'  => 'required|in_list[active,inactive]',
            'image'   => 'permit_empty|is_image[image]|max_size[image,2048]',
        ])) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        try {
            // Handle file upload
            $image = $this->request->getFile('image');
            $imageName = $slider['image'];

            // Check if a new image was uploaded
            if ($image && $image->getError() !== UPLOAD_ERR_NO_FILE) {
                // Make sure the uploads directory exists
                $uploadPath = ROOTPATH . 'public/uploads/slider';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0777, true);
                }

                // Delete old image if exists
                if ($slider['image'] && file_exists($uploadPath . '/' . $slider['image'])) {
                    unlink($uploadPath . '/' . $slider['image']);
                }

                // Generate a new random name for the image
                $imageName = $image->getRandomName();

                // Move the uploaded file to the destination
                $image->move($uploadPath, $imageName);

                if (!$image->hasMoved()) {
                    return redirect()->back()->withInput()->with('errors', ['image' => 'Failed to save the image: ' . $image->getErrorString()]);
                }
            }

            // Prepare data for update
            $data = [
                'message'   => $this->request->getPost('message'),
                'paragraph' => $this->request->getPost('paragraph'),
                'image'     => $imageName,
                'order'     => $this->request->getPost('order') ?? 1,
                'status'    => $this->request->getPost('status') ?? 'active',
            ];

            // Update data
            $this->homeSliderModel->update($id, $data);

            return redirect()->to('/home-slider')->with('success', 'Slider item updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating slider: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('errors', ['error' => 'An error occurred: ' . $e->getMessage()]);
        }
    }

    public function delete($id = null)
    {
        $slider = $this->homeSliderModel->find($id);

        if (!$slider) {
            throw new PageNotFoundException('Slider item not found');
        }

        try {
            // Delete image if exists
            $uploadPath = ROOTPATH . 'public/uploads/slider';
            if ($slider['image'] && file_exists($uploadPath . '/' . $slider['image'])) {
                unlink($uploadPath . '/' . $slider['image']);
            }

            $this->homeSliderModel->delete($id);

            return redirect()->to('/home-slider')->with('success', 'Slider item deleted successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error deleting slider: ' . $e->getMessage());
            return redirect()->back()->with('errors', ['error' => 'An error occurred while deleting: ' . $e->getMessage()]);
        }
    }
}
