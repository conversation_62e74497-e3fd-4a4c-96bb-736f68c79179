<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>


<div class="card">
    <div class="card-body">
        <?php if (session()->has('errors')): ?>
            <div class="alert alert-danger">
                <ul>
                    <?php foreach (session('errors') as $error): ?>
                        <li><?= $error ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <form action="<?= site_url('about/update/' . $about['id']) ?>" method="post" enctype="multipart/form-data">
            <div class="mb-3">
                <label for="title" class="form-label">Title</label>
                <input type="text" class="form-control" id="title" name="title" value="<?= old('title', $about['title']) ?>" required>
            </div>

            <div class="mb-3">
                <label for="subtitle" class="form-label">Subtitle</label>
                <input type="text" class="form-control" id="subtitle" name="subtitle" value="<?= old('subtitle', $about['subtitle'] ?? '') ?>">
                <div class="form-text">Secondary title or heading (e.g., your name)</div>
            </div>

            <div class="mb-3">
                <label for="content" class="form-label">Content</label>
                <textarea class="form-control" id="content" name="content" rows="6" required><?= old('content', $about['content']) ?></textarea>
            </div>

            <div class="d-grid">
                <button type="submit" class="btn btn-primary">Update About Page</button>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?>
