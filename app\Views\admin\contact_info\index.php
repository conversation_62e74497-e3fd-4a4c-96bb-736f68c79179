<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-end align-items-center mb-4">
    <a href="<?= site_url('contact-info/edit') ?>" class="btn btn-primary">
        <i class="bi bi-pencil-square"></i> Edit Contact Information
    </a>
</div>


<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Contact Details</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered">
                <tbody>
                    <tr>
                        <th style="width: 200px;">Email</th>
                        <td><?= $info['email'] ?></td>
                    </tr>
                    <tr>
                        <th>Phone</th>
                        <td><?= $info['phone'] ?></td>
                    </tr>
                    <tr>
                        <th>Address</th>
                        <td><?= $info['address'] ?></td>
                    </tr>
                    <tr>
                        <th>WhatsApp</th>
                        <td>
                            <?php if ($info['whatsapp']): ?>
                                <a href="<?= $info['whatsapp'] ?>" target="_blank"><?= $info['whatsapp'] ?></a>
                            <?php else: ?>
                                <span class="text-muted">Not set</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th>Map URL</th>
                        <td>
                            <?php if ($info['map_url']): ?>
                                <a href="<?= $info['map_url'] ?>" target="_blank"><?= $info['map_url'] ?></a>
                            <?php else: ?>
                                <span class="text-muted">Not set</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
