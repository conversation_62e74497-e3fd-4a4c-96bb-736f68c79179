<?php

namespace App\Controllers\Api;

use App\Models\HomeSliderModel;

class Home extends BaseController
{
    protected $homeSliderModel;

    public function __construct()
    {
        $this->homeSliderModel = new HomeSliderModel();
    }

    public function slider()
    {
        $sliders = $this->homeSliderModel->where('status', 'active')
                                        ->orderBy('order', 'ASC')
                                        ->findAll();
        
        $response = [
            'status' => 'success',
            'data' => [
                'messages' => [],
                'paragraphs' => [],
                'images' => []
            ]
        ];
        
        foreach ($sliders as $slider) {
            $response['data']['messages'][] = $slider['message'];
            $response['data']['paragraphs'][] = $slider['paragraph'];
            
            if ($slider['image']) {
                $response['data']['images'][] = base_url('public/uploads/slider/' . $slider['image']);
            }
        }

        return $this->respond($response);
    }
}
