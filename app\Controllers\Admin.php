<?php

namespace App\Controllers;

use App\Models\ServiceModel;
use App\Models\TeamModel;
use App\Models\GalleryModel;
use App\Models\ContactModel;
use App\Models\AboutModel;
use App\Models\HomeSliderModel;
use App\Models\InvoiceModel;
use App\Models\ClientModel;
use App\Models\BlogPostModel;
use App\Models\BlogCategoryModel;
use App\Models\BlogCommentModel;

class Admin extends BaseController
{
    public function index()
    {
        $serviceModel = new ServiceModel();
        $teamModel = new TeamModel();
        $galleryModel = new GalleryModel();
        $contactModel = new ContactModel();
        $homeSliderModel = new HomeSliderModel();
        $invoiceModel = new InvoiceModel();
        $clientModel = new ClientModel();
        $blogPostModel = new BlogPostModel();
        $blogCategoryModel = new BlogCategoryModel();
        $blogCommentModel = new BlogCommentModel();

        // Get recent invoices
        $recentInvoices = $invoiceModel->select('invoices.*, clients.name as client_name')
            ->join('clients', 'clients.id = invoices.client_id')
            ->orderBy('invoices.created_at', 'DESC')
            ->limit(5)
            ->find();

        // Get invoice statistics
        $invoiceStats = $invoiceModel->getInvoiceStatusStats();
        $totalInvoices = $invoiceStats['total']['count'];
        $paidInvoices = $invoiceStats['paid']['count'];
        $draftInvoices = $invoiceStats['draft']['count'];
        $sentInvoices = $invoiceStats['sent']['count'];
        $cancelInvoices = $invoiceStats['cancelled']['count'];

        // Calculate total revenue
        $totalRevenue = $invoiceModel->selectSum('total')->where('status', 'paid')->first()['total'] ?? 0;

        // Calculate previous month's revenue
        $currentMonth = date('m');
        $currentYear = date('Y');
        $previousMonth = $currentMonth - 1;
        $previousMonthYear = $currentYear;

        // Handle January case (previous month is December of previous year)
        if ($previousMonth == 0) {
            $previousMonth = 12;
            $previousMonthYear = $currentYear - 1;
        }

        // Get start and end dates for previous month
        $previousMonthStart = "$previousMonthYear-$previousMonth-01";
        $previousMonthEnd = date('Y-m-t', strtotime($previousMonthStart));

        // Get current month's revenue
        $currentMonthStart = "$currentYear-$currentMonth-01";
        $currentMonthEnd = date('Y-m-t', strtotime($currentMonthStart));
        $currentMonthRevenue = $invoiceModel->selectSum('total')
            ->where('status', 'paid')
            ->where('invoice_date >=', $currentMonthStart)
            ->where('invoice_date <=', $currentMonthEnd)
            ->first()['total'] ?? 0;

        // Get previous month's revenue
        $previousMonthRevenue = $invoiceModel->selectSum('total')
            ->where('status', 'paid')
            ->where('invoice_date >=', $previousMonthStart)
            ->where('invoice_date <=', $previousMonthEnd)
            ->first()['total'] ?? 0;

        // Calculate percentage change
        $revenueChange = 0;
        $revenueChangeType = 'neutral';

        if ($previousMonthRevenue > 0) {
            $revenueChange = round((($currentMonthRevenue - $previousMonthRevenue) / $previousMonthRevenue) * 100);
            $revenueChangeType = $revenueChange >= 0 ? 'increase' : 'decrease';
            // Convert negative to positive for display
            $revenueChange = abs($revenueChange);
        } elseif ($currentMonthRevenue > 0) {
            // If previous month was 0 but current month has revenue, show 100% increase
            $revenueChange = 100;
            $revenueChangeType = 'increase';
        }

        // Get revenue data for the last 6 months for the chart
        $monthlyRevenueData = [];
        $monthlyRevenueLabels = [];

        for ($i = 5; $i >= 0; $i--) {
            $month = date('m', strtotime("-$i months"));
            $year = date('Y', strtotime("-$i months"));

            $monthStart = "$year-$month-01";
            $monthEnd = date('Y-m-t', strtotime($monthStart));

            $monthlyRevenue = $invoiceModel->selectSum('total')
                ->where('status', 'paid')
                ->where('invoice_date >=', $monthStart)
                ->where('invoice_date <=', $monthEnd)
                ->first()['total'] ?? 0;

            $monthlyRevenueData[] = $monthlyRevenue;
            $monthlyRevenueLabels[] = date('M Y', strtotime($monthStart));
        }

        // Get blog statistics
        $totalPosts = $blogPostModel->countAllResults();
        $publishedPosts = $blogPostModel->where('status', 'published')->countAllResults();
        $draftPosts = $blogPostModel->where('status', 'draft')->countAllResults();
        $totalCategories = $blogCategoryModel->countAllResults();
        $totalComments = $blogCommentModel->countAllResults();
        $pendingComments = $blogCommentModel->where('status', 'pending')->countAllResults();

        // Get recent blog posts
        $recentPosts = $blogPostModel->select('blog_posts.*, blog_categories.name as category_name')
            ->join('blog_categories', 'blog_categories.id = blog_posts.category_id')
            ->orderBy('blog_posts.created_at', 'DESC')
            ->limit(5)
            ->find();

        $data = [
            'title'               => 'Admin Dashboard',
            'slidersCount'        => $homeSliderModel->countAllResults(),
            'servicesCount'       => $serviceModel->countAllResults(),
            'teamCount'           => $teamModel->countAllResults(),
            'galleryCount'        => $galleryModel->countAllResults(),
            'contactCount'        => $contactModel->countAllResults(),
            'unreadMessages'      => $contactModel->where('status', 'unread')->countAllResults(),
            'clientsCount'        => $clientModel->countAllResults(),
            'invoicesCount'       => $totalInvoices,
            'paidInvoices'        => $paidInvoices,
            'draftInvoices'       => $draftInvoices,
            'sentInvoices'        => $sentInvoices,
            'cancelInvoices'      => $cancelInvoices,
            'invoiceStats'        => $invoiceStats,
            'totalRevenue'        => $totalRevenue,
            'currentMonthRevenue' => $currentMonthRevenue,
            'previousMonthRevenue'=> $previousMonthRevenue,
            'revenueChange'       => $revenueChange,
            'revenueChangeType'   => $revenueChangeType,
            'monthlyRevenueData'  => $monthlyRevenueData,
            'monthlyRevenueLabels'=> $monthlyRevenueLabels,
            'recentInvoices'      => $recentInvoices,
            'totalPosts'          => $totalPosts,
            'publishedPosts'      => $publishedPosts,
            'draftPosts'          => $draftPosts,
            'totalCategories'     => $totalCategories,
            'totalComments'       => $totalComments,
            'pendingComments'     => $pendingComments,
            'recentPosts'         => $recentPosts,
        ];

        return view('admin/dashboard', $data);
    }
}
