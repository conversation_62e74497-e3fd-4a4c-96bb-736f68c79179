<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --primary-light: #a5b4fc;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --info-color: #06b6d4;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --light-color: #f8fafc;
            --dark-color: #1e293b;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
        }

        body {
            background: linear-gradient(135deg, #4338ca 0%, #6366f1 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Poppins', sans-serif;
            padding: 2rem 0;
        }

        .card {
            border: none;
            border-radius: 1.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            max-width: 400px;
            margin: 0 auto;
        }

        .card:hover {
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            transform: translateY(-5px);
        }

        .card-body {
            padding: 1.75rem;
        }

        .form-control {
            border: 1px solid var(--gray-200);
            border-radius: 0.5rem;
            padding: 0.6rem 0.8rem;
            font-size: 0.8rem;
            font-weight: 400;
            color: var(--gray-800);
            background-color: var(--light-color);
            transition: all 0.3s ease;
        }

        .form-control:focus {
            box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
            border-color: var(--primary-color);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            border-radius: 0.5rem;
            padding: 0.6rem 1rem;
            font-size: 0.9rem;
            font-weight: 600;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .btn-block {
            display: block;
            width: 100%;
        }

        .login-heading {
            font-weight: 700;
            color: var(--gray-800);
            font-size: 1.5rem;
            margin-bottom: 0.25rem;
        }

        .login-subheading {
            color: var(--gray-500);
            margin-bottom: 1.5rem;
            font-weight: 400;
            font-size: 0.9rem;
        }

        .form-label {
            font-size: 0.8rem;
            color: var(--gray-600);
        }

        .input-group-text {
            border-radius: 0.5rem 0 0 0.5rem;
            background-color: var(--gray-100);
            border: 1px solid var(--gray-200);
            border-right: none;
            color: var(--gray-500);
            padding: 0.6rem 0.8rem;
        }

        .form-control.password {
            border-radius: 0 0.5rem 0.5rem 0;
            border-left: none;
        }

        .alert {
            border-radius: 0.75rem;
            border: none;
            padding: 1rem;
            margin-bottom: 1.5rem;
            font-weight: 500;
        }

        .alert-danger {
            background-color: rgba(239, 68, 68, 0.1);
            color: #b91c1c;
        }

        .alert-success {
            background-color: rgba(16, 185, 129, 0.1);
            color: #047857;
        }

        .back-to-login {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.85rem;
            font-weight: 500;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
        }

        .back-to-login:hover {
            color: var(--primary-dark);
            transform: translateX(-3px);
        }

        .brand-logo {
            margin-bottom: 1.25rem;
            text-align: center;
        }

        .brand-logo svg {
            height: 30px;
            width: auto;
        }

        .form-check-input {
            width: 1.1em;
            height: 1.1em;
            margin-top: 0.2em;
            background-color: var(--light-color);
            border: 2px solid var(--gray-300);
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .form-check-label {
            color: var(--gray-600);
            font-weight: 500;
            font-size: 0.85rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-4 col-md-6 col-sm-10">
                <div class="card">
                    <div class="card-body">
                        <div class="brand-logo">
                            <svg width="130" height="30" viewBox="0 0 180 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 0C8.954 0 0 8.954 0 20C0 31.046 8.954 40 20 40C31.046 40 40 31.046 40 20C40 8.954 31.046 0 20 0ZM20 30C14.477 30 10 25.523 10 20C10 14.477 14.477 10 20 10C25.523 10 30 14.477 30 20C30 25.523 25.523 30 20 30Z" fill="#6366F1"/>
                                <path d="M60 10H50V30H60C66.627 30 72 24.627 72 20C72 15.373 66.627 10 60 10Z" fill="#6366F1"/>
                                <path d="M90 10H80V30H90C96.627 30 102 24.627 102 20C102 15.373 96.627 10 90 10Z" fill="#6366F1"/>
                                <path d="M120 30V10H110V30H120Z" fill="#6366F1"/>
                                <path d="M140 10H130V30H140C146.627 30 152 24.627 152 20C152 15.373 146.627 10 140 10Z" fill="#6366F1"/>
                                <path d="M170 10H160V30H170C176.627 30 182 24.627 182 20C182 15.373 176.627 10 170 10Z" fill="#6366F1"/>
                            </svg>
                        </div>

                        <div class="text-center mb-4">
                            <h3 class="login-heading">Reset Password</h3>
                            <p class="login-subheading">Enter your new password below</p>
                        </div>

                        <?php if (session()->has('error')): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                    <div><?= session('error') ?></div>
                                </div>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>

                        <?php if (session()->has('message')): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-check-circle-fill me-2"></i>
                                    <div><?= session('message') ?></div>
                                </div>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>

                        <?php if (session()->has('errors')): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <div class="d-flex">
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                    <ul class="mb-0 ps-2">
                                        <?php foreach (session('errors') as $error): ?>
                                            <li><?= $error ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>

                        <form action="<?= site_url('auth/processResetPassword') ?>" method="post">
                            <input type="hidden" name="token" value="<?= $token ?>">
                            <input type="hidden" name="email" value="<?= $email ?>">

                            <div class="mb-3">
                                <label for="password" class="form-label fw-medium mb-1">New Password</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-lock"></i></span>
                                    <input type="password" class="form-control password" id="password" name="password" placeholder="Enter new password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="confirm_password" class="form-label fw-medium mb-1">Confirm Password</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-lock-fill"></i></span>
                                    <input type="password" class="form-control password" id="confirm_password" name="confirm_password" placeholder="Confirm your password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle me-2"></i>Reset Password
                                </button>
                            </div>

                            <div class="text-center">
                                <a href="<?= site_url('auth/login') ?>" class="back-to-login">
                                    <i class="bi bi-arrow-left me-2"></i> Back to Login
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="text-center mt-4 text-white">
                    <small>&copy; <?= date('Y') ?> Admin Dashboard. All rights reserved.</small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('bi-eye');
                icon.classList.add('bi-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('bi-eye-slash');
                icon.classList.add('bi-eye');
            }
        });

        document.getElementById('toggleConfirmPassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('confirm_password');
            const icon = this.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('bi-eye');
                icon.classList.add('bi-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('bi-eye-slash');
                icon.classList.add('bi-eye');
            }
        });
    </script>
</body>
</html>
