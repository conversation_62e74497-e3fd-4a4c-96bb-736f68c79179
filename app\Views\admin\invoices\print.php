<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice #<?= $invoice['invoice_number'] ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            font-size: 12px;
            line-height: 1.4;
        }
        .invoice-container {
            background-color: #fff;
            max-width: 800px;
            margin: 0 auto;
            padding: 0;
            position: relative;
            border: 1px solid #eee;
            overflow: hidden;
        }

        /* Watermark styling */
        .watermark {
            position: absolute;
            top: 60%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1;
            opacity: 0.03;
            pointer-events: none;
            width: 80%;
            max-width: 600px;
            height: auto;
        }
        .invoice-content {
            padding: 50px 30px 0 30px; 
            position: relative;
            z-index: 2; 
        }
        .invoice-top-design {
            position: relative;
            height: 60px; 
            background: #0d6efd;
            margin: 0;
            width: 100%;
            border-radius: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding:0px;
            overflow: visible; 
            z-index: 2;
        }

        /* .emblem-container {
            position: absolute;
            left: 49%;
            top: 64px; 
            transform: translateX(-50%);
            z-index: 10;
            text-align: center;
        } */

        .advocate-text {
            color: #ffffff;
            font-size: 48px;
            font-weight: 700;
            text-transform: uppercase;
            margin: 0;
            padding-top: 10px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
            letter-spacing:10px;
            text-align: center;
            width: 100%;
            background-color: #0d6efd;
            position: relative;
            z-index: 2; /* Ensure it appears above the watermark */
        }

        /* .emblem-image {
            height: 110px;
            background:#d32f2f;
            border-radius:0 0 30% 30%;
            padding: 10px 
        } */
         

        /* Header text styling */
        .header-text {
            color: white;
            font-size: 30px;
            font-weight: 700;
            text-transform: uppercase;
            text-shadow: none; /* Removed text shadow */
            text-align: center;
            width: 100%;

        }
        .invoice-header {
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .invoice-title {
            font-size: 24px;
            color: #3498db;
            margin: 0;
            font-weight: 700;
        }
        .invoice-number {
            font-size: 14px;
            color: #222;
        }
        .invoice-meta {
            margin-bottom: 15px;
        }
        .invoice-meta-item {
            margin-bottom: 3px;
        }
        .invoice-address {
            margin-bottom: 15px;
        }
        .invoice-address h6 {
            font-size: 14px;
            color: #3498db;
            margin-bottom: 5px;
            font-weight: 600;
        }
        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        .invoice-table th {
            background-color: #f2f9ff;
            color: #3498db;
            font-weight: 600;
            text-align: left;
            padding: 8px;
            border: 1px solid #ddd;
        }
        .invoice-table td {
            padding: 8px;
            border: 1px solid #ddd;
            vertical-align: top;
        }
        .invoice-table .text-end {
            text-align: right;
        }
        .invoice-total {
            background-color: #f2f9ff;
            font-weight: 700;
        }
        .invoice-notes {
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-size: 11px;
        }
        .invoice-payment {
            border-top: 1px solid #eee;
            padding-top: 10px;
            margin-top: 10px;
        }
        .invoice-payment h6 {
            font-size: 14px;
            color: #3498db;
            margin-bottom: 5px;
            font-weight: 600;
        }
        .qr-code {
            margin:10px;
            max-width: 100px;
            height: auto;
        }
        .invoice-footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 5px;
            border-top: 2px solid #000;
            font-size: 11px;
            color: #777;
            position: relative;
        }

        .badge {
            display: inline-block;
            padding: 3px 6px;
            font-size: 10px;
            font-weight: 600;
            border-radius: 3px;
            text-transform: uppercase;
        }
        .badge-draft { background-color: #6c757d; color: white; }
        .badge-sent { background-color: #007bff; color: white; }
        .badge-paid { background-color: #28a745; color: white; }
        .badge-cancelled { background-color: #dc3545; color: white; }

        .print-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background-color: #fff;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        @media print {
            .print-controls {
                display: none !important;
            }
            body {
                background-color: #fff;
            }
            .invoice-container {
                padding: 0;
                box-shadow: none;
                border: none;
                max-width: 100%;
            }
            .advocate-text {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            .invoice-top-design {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            .invoice-footer::before {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            .watermark {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                color-adjust: exact !important;
                opacity: 0.15 !important;
            }
            @page {
                size: A4;
                margin: 0cm;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Centered Watermark -->
        <img src="<?= base_url('public/assets/adv logo markup.png') ?>" alt="Watermark" class="watermark">

        <div class="advocate-text">ADVOCATE</div>

        <div class="invoice-top-design">
            <div class="header-text"> HIGH COURT LUCKNOW BENCH</div>
            <!-- <div class="header-text">&nbsp;&nbsp; LUCKNOW  BENCH </div> -->
        </div>

        <!-- <div class="emblem-container">
            <img src="<?= base_url('public/assets/hc-logo.png') ?>" alt="Indian Emblem" class="emblem-image">
        </div> -->

        <div class="invoice-content">
            <div class="invoice-header">
                <div class="row">
                    <div class="col-7">
                        <div class="invoice-number" style="font-size: 16px;"><strong>Invoice #<?= $invoice['invoice_number'] ?></strong></div>
                    </div>
                    <div class="col-5 text-end">
                        <div>
                            <?php
                            $statusClass = [
                                'draft' => 'badge-draft',
                                'sent' => 'badge-sent',
                                'paid' => 'badge-paid',
                                'cancelled' => 'badge-cancelled'
                            ];
                            ?>
                            <span class="badge <?= $statusClass[$invoice['status']] ?? 'badge-draft' ?>">
                                <?= ucfirst($invoice['status']) ?>
                            </span>
                        </div>
                        <div class="mt-2">
                            <strong>Date:</strong> <?= date('d/m/Y', strtotime($invoice['invoice_date'])) ?>
                        </div>
                        <?php if (!empty($invoice['due_date'])): ?>
                            <div>
                                <strong>Due Date:</strong> <?= date('d/m/Y', strtotime($invoice['due_date'])) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

        <div class="row">
            <div class="col-6">
                <div class="invoice-address">
                    <h6>From:</h6>
                    <div><strong>KRISHNA RAM YADAV</strong></div>
                    <div>Advocate</div>
                    <div>Phone: 9451732775</div>
                    <div>Email: <EMAIL></div>
                </div>
            </div>
            <div class="col-6">
                <div class="invoice-address">
                    <h6>To:</h6>
                    <div><strong><?= $invoice['client_name'] ?></strong></div>
                    <?php if (!empty($invoice['client_phone'])): ?>
                        <div>Phone: <?= $invoice['client_phone'] ?></div>
                    <?php endif; ?>
                    <?php if (!empty($invoice['client_email'])): ?>
                        <div>Email: <?= $invoice['client_email'] ?></div>
                    <?php endif; ?>
                    <?php if (!empty($invoice['client_address'])): ?>
                        <div>Address: <?= $invoice['client_address'] ?></div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="invoice-meta">
            <div class="row">
                <div class="col-12">
                    <div class="invoice-meta-item"><strong>Subject:</strong> <?= $invoice['subject'] ?></div>
                </div>
            </div>
        </div>

        <table class="invoice-table">
            <thead>
                <tr>
                    <th width="75%">Description</th>
                    <th width="25%" class="text-end">Fee (₹)</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($items)): ?>
                    <tr>
                        <td colspan="2" class="text-center">No items found</td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($items as $item): ?>
                        <tr>
                            <td><?= $item['description'] ?></td>
                            <td class="text-end"><?= number_format($item['fee'], 2) ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
                <tr class="invoice-total">
                    <td class="text-end"><strong>Total:</strong></td>
                    <td class="text-end"><strong>₹<?= number_format($invoice['total'], 2) ?></strong></td>
                </tr>
            </tbody>
        </table>

        <?php if (!empty($invoice['notes'])): ?>
            <div class="invoice-notes">
                <h6>Notes:</h6>
                <p><?= nl2br($invoice['notes']) ?></p>
            </div>
        <?php endif; ?>

        <div class="invoice-payment">
            <div class="row">
                <div class="col-6">
                    <?php if (!empty($invoice['upi_id']) || !empty($invoice['qr_code'])): ?>
                        <h6>Payment Information:</h6>
                        <div class="d-flex">
                            <div class="me-3">
                                <?php if (!empty($invoice['upi_id'])): ?>
                                    <div><strong>UPI ID:</strong> <?= $invoice['upi_id'] ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php if (!empty($invoice['qr_code'])): ?>
                            <div>
                                <img src="<?= base_url('public/uploads/qr_codes/' . $invoice['qr_code']) ?>" alt="Payment QR Code" class="qr-code">
                                <div class="mt-1" style="font-size: 11px; margin-left: 25px;">Scan to pay</div>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
                <div class="col-6 text-end">
                    <div>
                        <h6 style="margin-right: 65px;">Signature:</h6>
                        <!-- Permanent signature image -->
                        <img src="<?= base_url('public/assets/signature.png') ?>" alt="Signature" style="max-height: 100px; max-width: 200px;">
                        <div class="mt-2">
                            <strong>KRISHNA RAM YADAV</strong><br>
                            Advocate
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="invoice-footer">
            <p style="margin-bottom: 0;">For any inquiries regarding this invoice, please contact: <EMAIL> | +91 9451732775</p>
        </div>
        </div><!-- End of invoice-content -->
    </div>

    <!-- Print Controls -->
    <div class="print-controls">
        <button class="btn btn-primary btn-sm" onclick="window.print()">
            <i class="bi bi-printer"></i> Print
        </button>
        <a href="<?= site_url('invoices') ?>" class="btn btn-secondary btn-sm ms-2">
            <i class="bi bi-arrow-left"></i> Back
        </a>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
