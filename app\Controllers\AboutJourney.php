<?php

namespace App\Controllers;

use App\Models\AboutJourneyModel;
use CodeIgniter\Exceptions\PageNotFoundException;

class AboutJourney extends BaseController
{
    protected $aboutJourneyModel;

    public function __construct()
    {
        $this->aboutJourneyModel = new AboutJourneyModel();
    }

    public function index()
    {
        $data = [
            'title'    => 'Manage Journey Milestones',
            'journeys' => $this->aboutJourneyModel->orderBy('year', 'ASC')->paginate(10, 'journey'),
            'pager'    => $this->aboutJourneyModel->pager,
        ];

        return view('admin/about/journey/index', $data);
    }

    public function new()
    {
        $data = [
            'title' => 'Add New Journey',
        ];

        return view('admin/about/journey/create', $data);
    }

    public function create()
    {
        if (!$this->validate($this->aboutJourneyModel->validationRules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'year'        => $this->request->getPost('year'),
            'title'       => $this->request->getPost('title'),
            'description' => $this->request->getPost('description'),
            'status'      => $this->request->getPost('status'),
        ];

        $this->aboutJourneyModel->insert($data);

        return redirect()->to('/about-journey')->with('success', 'Journey milestone added successfully');
    }

    public function edit($id = null)
    {
        $journey = $this->aboutJourneyModel->find($id);

        if (!$journey) {
            throw new PageNotFoundException('Journey milestone not found');
        }

        $data = [
            'title'   => 'Edit Journey',
            'journey' => $journey,
        ];

        return view('admin/about/journey/edit', $data);
    }

    public function update($id = null)
    {
        $journey = $this->aboutJourneyModel->find($id);

        if (!$journey) {
            throw new PageNotFoundException('Journey milestone not found');
        }

        if (!$this->validate($this->aboutJourneyModel->validationRules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'year'        => $this->request->getPost('year'),
            'title'       => $this->request->getPost('title'),
            'description' => $this->request->getPost('description'),
            'status'      => $this->request->getPost('status'),
        ];

        $this->aboutJourneyModel->update($id, $data);

        return redirect()->to('/about-journey')->with('success', 'Journey milestone updated successfully');
    }

    public function delete($id = null)
    {
        $journey = $this->aboutJourneyModel->find($id);

        if (!$journey) {
            throw new PageNotFoundException('Journey milestone not found');
        }

        $this->aboutJourneyModel->delete($id);

        return redirect()->to('/about-journey')->with('success', 'Journey milestone deleted successfully');
    }
}
