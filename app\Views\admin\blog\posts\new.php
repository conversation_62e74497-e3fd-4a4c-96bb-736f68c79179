<?= $this->extend('admin/layout') ?>

<?= $this->section('styles') ?>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    .select2-container--default .select2-selection--multiple {
        border: 1px solid #ced4da;
        min-height: 38px;

    }

</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-end align-items-center mb-4">
    <a href="<?= site_url('blog-posts') ?>" class="btn btn-secondary">
        <i class="bi bi-arrow-left me-1"></i> Back to Posts
    </a>
</div>

<?= $this->include('admin/partials/alerts') ?>

<form action="<?= site_url('blog-posts/create') ?>" method="post" enctype="multipart/form-data">
    <?= csrf_field() ?>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-body">
                    <div class="mb-3">
                        <label for="title" class="form-label">Post Title <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="title" name="title" value="<?= old('title') ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="slug" class="form-label">Slug <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="slug" name="slug" value="<?= old('slug') ?>" required>
                        <div class="form-text">The "slug" is the URL-friendly version of the title.</div>
                    </div>


<!-- ****************************************************************-->

                    <div class="mb-3">
                        <label for="content" class="form-label">Content <span class="text-danger">*</span></label>
                        <div id="editor-container">
                            <div id="toolbar-container"></div>
                            <div id="editor"></div>
                        </div>
                        <textarea class="form-control d-none" id="content" name="content" rows="10"><?= old('content') ?></textarea>
                    </div>

                    <!-- Google Fonts for CKEditor -->
<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&family=Open+Sans:wght@400;700&family=Lato:wght@400;700&family=Montserrat:wght@400;700&family=Poppins:wght@400;700&family=Playfair+Display:wght@400;700&display=swap">

<!-- CKEditor 5 from CDN -->
<script src="https://cdn.ckeditor.com/ckeditor5/40.2.0/decoupled-document/ckeditor.js"></script>

<!-- Custom CKEditor CSS -->
<link rel="stylesheet" href="<?= base_url('public/assets/ckeditor5-custom/css/ckeditor-custom.css') ?>">

<!-- Custom CKEditor initialization script -->
<script src="<?= base_url('public/assets/ckeditor5-custom/js/ckeditor-init.js') ?>"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get the content from the textarea (for consistency with edit.php)
        const contentTextarea = document.getElementById('content');
        const initialContent = contentTextarea.value;

        // Initialize CKEditor with custom configuration
        initCKEditor(
            '#editor',                // Editor element selector
            '#toolbar-container',     // Toolbar container selector
            '#content',               // Hidden textarea selector
            'Enter your content here...', // Placeholder text
            '<?= site_url('upload/ckeditor') ?>', // Upload URL using CodeIgniter's site_url helper
            function(editor) {
                // Set the initial content after the editor is ready (if any)
                if (initialContent) {
                    editor.setData(initialContent);
                    console.log('Set initial content to editor');
                }
            }
        );
    });
</script>

<!-- ****************************************************************-->

                    <div class="mb-3">
                        <label for="excerpt" class="form-label">Excerpt</label>
                        <textarea class="form-control" id="excerpt" name="excerpt" rows="3"><?= old('excerpt') ?></textarea>
                        <div class="form-text">A short summary of the post. If left empty, it will be generated from the content.</div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">SEO Settings</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="meta_title" class="form-label">Meta Title</label>
                        <input type="text" class="form-control" id="meta_title" name="meta_title" value="<?= old('meta_title') ?>">
                        <div class="form-text">If left empty, the post title will be used.</div>
                    </div>

                    <div class="mb-3">
                        <label for="meta_description" class="form-label">Meta Description</label>
                        <textarea class="form-control" id="meta_description" name="meta_description" rows="2"><?= old('meta_description') ?></textarea>
                        <div class="form-text">If left empty, the post excerpt will be used.</div>
                    </div>

                    <div class="mb-3">
                        <label for="meta_keywords" class="form-label">Meta Keywords</label>
                        <input type="text" class="form-control" id="meta_keywords" name="meta_keywords" value="<?= old('meta_keywords') ?>">
                        <div class="form-text">Comma-separated list of keywords.</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Publish</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="draft" <?= old('status') === 'draft' ? 'selected' : '' ?>>Draft</option>
                            <option value="published" <?= old('status') === 'published' ? 'selected' : '' ?>>Published</option>
                        </select>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-save me-1"></i> Save Post
                        </button>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Category</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="category_id" class="form-label">Category <span class="text-danger">*</span></label>
                        <select class="form-select" id="category_id" name="category_id" required>
                            <option value="">Select Category</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?= $category['id'] ?>" <?= old('category_id') == $category['id'] ? 'selected' : '' ?>>
                                    <?= esc($category['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="d-grid">
                        <a href="<?= site_url('blog-categories/new') ?>" class="btn btn-outline-primary btn-sm" target="_blank">
                            <i class="bi bi-plus-circle me-1"></i> Add New Category
                        </a>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Tags</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="tags" class="form-label">Tags</label>
                        <select class="form-select" id="tags" name="tags[]" multiple>
                            <?php foreach ($tags as $tag): ?>
                                <option value="<?= $tag['id'] ?>" <?= in_array($tag['id'], old('tags', [])) ? 'selected' : '' ?>>
                                    <?= esc($tag['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="d-grid">
                        <a href="<?= site_url('blog-tags/new') ?>" class="btn btn-outline-primary btn-sm" target="_blank">
                            <i class="bi bi-plus-circle me-1"></i> Add New Tag
                        </a>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Featured Image</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="featured_image" class="form-label">Upload Image</label>
                        <input class="form-control" type="file" id="featured_image" name="featured_image" accept="image/*">
                        <div class="form-text">Recommended size: 1200x630 pixels.</div>
                    </div>

                    <div id="image-preview" class="mt-3 d-none">
                        <img src="" alt="Preview" class="img-fluid img-thumbnail">
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Select2
    $('#tags').select2({
        placeholder: 'Select tags',
        allowClear: true
    });

    // Generate slug from title
    const titleInput = document.getElementById('title');
    const slugInput = document.getElementById('slug');

    titleInput.addEventListener('blur', function() {
        if (slugInput.value === '') {
            fetch('<?= site_url('blog-posts/generate-slug') ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: 'title=' + encodeURIComponent(titleInput.value) + '&<?= csrf_token() ?>=' + encodeURIComponent('<?= csrf_hash() ?>')
            })
            .then(response => response.json())
            .then(data => {
                slugInput.value = data.slug;
            });
        }
    });

    // Image preview
    const featuredImage = document.getElementById('featured_image');
    const imagePreview = document.getElementById('image-preview');
    const previewImg = imagePreview.querySelector('img');

    featuredImage.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                previewImg.src = e.target.result;
                imagePreview.classList.remove('d-none');
            }

            reader.readAsDataURL(this.files[0]);
        } else {
            imagePreview.classList.add('d-none');
        }
    });
});
</script>


<?= $this->endSection() ?>
