<?php $pager->setSurroundCount(2) ?>

<nav aria-label="<?= lang('Pager.pageNavigation') ?>" class="mt-4">
    <div class="d-flex flex-column flex-md-row justify-content-between align-items-center mb-3">
        <div class="text-muted mb-2 mb-md-0">
            <span class="badge bg-primary rounded-pill px-3 py-2 me-2">
                <i class="bi bi-file-text me-1"></i>
                <span class="fw-medium"><?= $pager->getTotal() ?></span> Total Records
            </span>
            Showing <span class="fw-medium text-primary"><?= $pager->getPerPageStart() ?></span>
            to <span class="fw-medium text-primary"><?= $pager->getPerPageEnd() ?></span>
            of <span class="fw-medium text-primary"><?= $pager->getTotal() ?></span> results
        </div>
    </div>

    <div class="d-flex justify-content-center">
        <nav aria-label="<?= lang('Pager.pageNavigation') ?>">
            <ul class="pagination pagination-circle">
                <?php if ($pager->hasPrevious()) : ?>
                    <li class="page-item d-none d-sm-block">
                        <a class="page-link" href="<?= $pager->getFirst() ?>" aria-label="<?= lang('Pager.first') ?>" title="<?= lang('Pager.first') ?>">
                            <i class="bi bi-chevron-double-left"></i>
                        </a>
                    </li>
                    <li class="page-item d-none d-sm-block">
                        <a class="page-link" href="<?= $pager->getPrevious() ?>" aria-label="<?= lang('Pager.previous') ?>" title="<?= lang('Pager.previous') ?>">
                            <i class="bi bi-chevron-left"></i>
                        </a>
                    </li>
                <?php endif ?>

                <?php foreach ($pager->links() as $link) : ?>
                    <li class="page-item <?= $link['active'] ? 'active' : '' ?>">
                        <a class="page-link" href="<?= $link['uri'] ?>">
                            <?= $link['title'] ?>
                        </a>
                    </li>
                <?php endforeach ?>

                <?php if ($pager->hasNext()) : ?>
                    <li class="page-item d-none d-sm-block">
                        <a class="page-link" href="<?= $pager->getNext() ?>" aria-label="<?= lang('Pager.next') ?>" title="<?= lang('Pager.next') ?>">
                            <i class="bi bi-chevron-right"></i>
                        </a>
                    </li>
                    <li class="page-item d-none d-sm-block">
                        <a class="page-link" href="<?= $pager->getLast() ?>" aria-label="<?= lang('Pager.last') ?>" title="<?= lang('Pager.last') ?>">
                            <i class="bi bi-chevron-double-right"></i>
                        </a>
                    </li>
                <?php endif ?>
            </ul>
        </nav>
    </div>
</nav>

<style>
/* Pagination Container */
.pagination-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 1.5rem 0;
    flex-wrap: wrap;
}

/* Pagination Arrows */
.pagination-arrows {
    display: flex;
    align-items: center;
}

.pagination-arrow {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    background-color: #fff;
    color: var(--primary-color, #6366f1);
    border: 1px solid var(--primary-color, #6366f1);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.pagination-arrow.prev {
    margin-right: 0.5rem;
}

.pagination-arrow.next {
    margin-left: 0.5rem;
}

.pagination-arrow i {
    font-size: 1.1rem;
}

.pagination-arrow.prev i {
    margin-right: 0.5rem;
}

.pagination-arrow.next i {
    margin-left: 0.5rem;
}

.pagination-arrow:hover {
    background-color: var(--primary-color, #6366f1);
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(99, 102, 241, 0.2);
}

.pagination-arrow.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Circle Pagination */
.pagination-circle {
    margin: 0 1rem;
}

.pagination-circle .page-link {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 3px;
    font-weight: 500;
    border: 1px solid var(--gray-200, #e2e8f0);
    color: var(--gray-700, #334155);
    transition: all 0.3s ease;
}

.pagination-circle .page-item.active .page-link {
    background-color: var(--primary-color, #6366f1);
    border-color: var(--primary-color, #6366f1);
    color: white;
    box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);
    transform: scale(1.1);
}

.pagination-circle .page-link:hover {
    background-color: rgba(99, 102, 241, 0.1);
    border-color: var(--primary-color, #6366f1);
    color: var(--primary-color, #6366f1);
    transform: translateY(-2px);
}

.pagination-circle .page-item.active .page-link:hover {
    background-color: var(--primary-color, #6366f1);
    color: white;
}

/* Responsive adjustments */
@media (max-width: 767px) {
    .pagination-container {
        flex-direction: column;
        gap: 1rem;
    }

    .pagination-arrows {
        width: 100%;
        justify-content: space-between;
    }

    .pagination-circle {
        margin: 1rem 0;
    }
}
</style>
