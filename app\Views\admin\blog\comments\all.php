<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-end align-items-center mb-4">
    <div>
        <a href="<?= site_url('blog-posts') ?>" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-1"></i> Back to Posts
        </a>
    </div>
</div>

<?= $this->include('admin/partials/alerts') ?>

<div class="card">
    <div class="card-header">
        <ul class="nav nav-tabs card-header-tabs">
            <li class="nav-item">
                <a class="nav-link <?= empty($status) ? 'active' : '' ?>" href="<?= site_url('blog-comments') ?>">All</a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?= $status === 'pending' ? 'active' : '' ?>" href="<?= site_url('blog-comments?status=pending') ?>">Pending</a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?= $status === 'approved' ? 'active' : '' ?>" href="<?= site_url('blog-comments?status=approved') ?>">Approved</a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?= $status === 'spam' ? 'active' : '' ?>" href="<?= site_url('blog-comments?status=spam') ?>">Spam</a>
            </li>
        </ul>
    </div>
    <div class="card-body">
        <?php if (empty($comments)): ?>
            <div class="text-center py-5">
                <div class="mb-3">
                    <i class="bi bi-chat-square-text fs-1 text-muted"></i>
                </div>
                <h5>No Comments Found</h5>
                <p class="text-muted">There are no comments in this section.</p>
            </div>
        <?php else: ?>
            <form action="<?= site_url('blog-comments/bulk-action') ?>" method="post" id="comments-form">
                <?= csrf_field() ?>

                <div class="mb-3">
                    <div class="d-flex align-items-center">
                        <select class="form-select form-select-sm w-auto me-2" name="action">
                            <option value="">Bulk Actions</option>
                            <option value="approved">Mark as Approved</option>
                            <option value="pending">Mark as Pending</option>
                            <option value="spam">Mark as Spam</option>
                            <option value="delete">Delete</option>
                        </select>
                        <button type="submit" class="btn btn-sm btn-primary">Apply</button>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead>
                            <tr>
                                <th width="30">
                                    <input type="checkbox" class="form-check-input" id="select-all">
                                </th>
                                <th>Author</th>
                                <th>Comment</th>
                                <th>Post</th>
                                <th width="120">Status</th>
                                <th width="150">Date</th>
                                <th width="150">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($comments as $comment): ?>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input comment-checkbox" name="ids[]" value="<?= $comment['id'] ?>">
                                    </td>
                                    <td>
                                        <div class="fw-bold"><?= esc($comment['name']) ?></div>
                                        <div class="small text-muted"><?= esc($comment['email']) ?></div>
                                        <?php if (!empty($comment['website'])): ?>
                                            <div class="small">
                                                <a href="<?= esc($comment['website']) ?>" target="_blank"><?= esc($comment['website']) ?></a>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= character_limiter(esc($comment['comment']), 100) ?></td>
                                    <td>
                                        <a href="<?= site_url('blog-posts/edit/' . $comment['post_id']) ?>"><?= esc($comment['post_title']) ?></a>
                                    </td>
                                    <td>
                                        <?php if ($comment['status'] === 'approved'): ?>
                                            <span class="badge bg-success">Approved</span>
                                        <?php elseif ($comment['status'] === 'pending'): ?>
                                            <span class="badge bg-warning text-dark">Pending</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">Spam</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= date('M d, Y g:i a', strtotime($comment['created_at'])) ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?= site_url('blog-comments/edit/' . $comment['id']) ?>" class="btn btn-outline-primary">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <a href="<?= site_url('blog-comments/delete/' . $comment['id']) ?>" class="btn btn-outline-danger" onclick="return confirm('Are you sure you want to delete this comment?')">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </form>

            <div class="mt-4">
                <?= $pager->links('comments', 'admin_full') ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select all checkbox
    const selectAll = document.getElementById('select-all');
    const commentCheckboxes = document.querySelectorAll('.comment-checkbox');

    if (selectAll) {
        selectAll.addEventListener('change', function() {
            commentCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }

    // Form submission validation
    const commentsForm = document.getElementById('comments-form');

    if (commentsForm) {
        commentsForm.addEventListener('submit', function(e) {
            const action = this.querySelector('select[name="action"]').value;
            const checkedBoxes = this.querySelectorAll('input[name="ids[]"]:checked');

            if (action === '') {
                e.preventDefault();
                alert('Please select an action.');
                return false;
            }

            if (checkedBoxes.length === 0) {
                e.preventDefault();
                alert('Please select at least one comment.');
                return false;
            }

            if (action === 'delete' && !confirm('Are you sure you want to delete the selected comments?')) {
                e.preventDefault();
                return false;
            }

            return true;
        });
    }
});
</script>
<?= $this->endSection() ?>
