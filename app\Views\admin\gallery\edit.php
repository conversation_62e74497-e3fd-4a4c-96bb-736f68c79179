<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-end mb-4">
    <a href="<?= site_url('gallery') ?>" class="btn btn-secondary">Back to Gallery</a>
</div>

<div class="card">
    <div class="card-body">
        <?php if (session()->has('errors')): ?>
            <div class="alert alert-danger">
                <ul>
                    <?php foreach (session('errors') as $error): ?>
                        <li><?= $error ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <form action="<?= site_url('gallery/update/' . $item['id']) ?>" method="post" enctype="multipart/form-data">




            <div class="mb-3">
                <label for="image" class="form-label">Image</label>
                <?php if ($item['image']): ?>
                    <div class="mb-2">
                        <img src="<?= base_url('public/uploads/gallery/' . $item['image']) ?>" alt="Gallery Image" width="100">
                    </div>
                <?php endif; ?>
                <input type="file" class="form-control" id="image" name="image" accept="image/jpeg,image/png,image/gif,image/jpg">
                <div class="form-text">Leave empty to keep the current image. Supported formats: JPG, JPEG, PNG, GIF</div>
            </div>



            <div class="mb-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status" required>
                    <option value="active" <?= old('status', $item['status']) === 'active' ? 'selected' : '' ?>>Active</option>
                    <option value="inactive" <?= old('status', $item['status']) === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                </select>
            </div>

            <div class="d-grid">
                <button type="submit" class="btn btn-primary">Update Gallery Item</button>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?>
