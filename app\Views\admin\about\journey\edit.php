<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-end align-items-center mb-4">
    <a href="<?= site_url('about-journey') ?>" class="btn btn-secondary">Back to List</a>
</div>

<?php if (session()->has('errors')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <ul class="mb-0">
            <?php foreach (session('errors') as $error): ?>
                <li><?= $error ?></li>
            <?php endforeach; ?>
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-body">
        <form action="<?= site_url('about-journey/update/' . $journey['id']) ?>" method="post">
            <div class="mb-3">
                <label for="year" class="form-label">Year</label>
                <input type="text" class="form-control" id="year" name="year" value="<?= old('year', $journey['year']) ?>" required>
                <div class="form-text">Enter the year for this milestone (e.g., 2018, 2020-2021)</div>
            </div>

            <div class="mb-3">
                <label for="title" class="form-label">Title</label>
                <input type="text" class="form-control" id="title" name="title" value="<?= old('title', $journey['title']) ?>" required>
                <div class="form-text">Enter a brief title for this milestone</div>
            </div>

            <div class="mb-3">
                <label for="description" class="form-label">Description</label>
                <textarea class="form-control" id="description" name="description" rows="4"><?= old('description', $journey['description']) ?></textarea>
                <div class="form-text">Provide details about this milestone (optional)</div>
            </div>

            <div class="mb-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status" required>
                    <option value="active" <?= old('status', $journey['status']) === 'active' ? 'selected' : '' ?>>Active</option>
                    <option value="inactive" <?= old('status', $journey['status']) === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                </select>
                <div class="form-text">Only active milestones will be displayed on the website</div>
            </div>

            <div class="d-grid">
                <button type="submit" class="btn btn-primary">Update Milestone</button>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?>
