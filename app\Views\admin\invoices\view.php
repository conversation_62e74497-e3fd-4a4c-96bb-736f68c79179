<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-end align-items-center mb-4">
    <div>
        <a href="<?= site_url('invoices/print/' . $invoice['id']) ?>" class="btn btn-info me-2" target="_blank">
            <i class="bi bi-printer"></i> Print
        </a>
        <a href="<?= site_url('invoices/edit/' . $invoice['id']) ?>" class="btn btn-warning me-2">
            <i class="bi bi-pencil"></i> Edit
        </a>
        <a href="<?= site_url('invoices/delete/' . $invoice['id']) ?>" class="btn btn-danger me-2" onclick="return confirm('Are you sure you want to delete this invoice?')">
            <i class="bi bi-trash"></i> Delete
        </a>
        <a href="<?= site_url('invoices') ?>" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Back to Invoices
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-body">
        <div class="row mb-4">
            <div class="col-md-6">
                <h5 class="mb-3">From:</h5>
                <div class="mb-1"><strong>KRISHNA RAM YADAV</strong></div>
                <div class="mb-1"><strong>Advocate</strong></div>
                <div class="mb-1">Phone: 9451732775</div>
                <div class="mb-1">Email: <EMAIL></div>
            </div>
            <div class="col-md-6 text-md-end">
                <h5 class="mb-3">To:</h5>
                <div class="mb-1"><strong><?= $invoice['client_name'] ?></strong></div>
                <?php if (!empty($invoice['client_phone'])): ?>
                    <div class="mb-1">Phone: <?= $invoice['client_phone'] ?></div>
                <?php endif; ?>
                <?php if (!empty($invoice['client_email'])): ?>
                    <div class="mb-1">Email: <?= $invoice['client_email'] ?></div>
                <?php endif; ?>
                <?php if (!empty($invoice['client_address'])): ?>
                    <div class="mb-1">Address: <?= $invoice['client_address'] ?></div>
                <?php endif; ?>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-6">
                <div class="mb-1"><strong>Invoice:</strong> <?= $invoice['invoice_number'] ?></div>
                <div class="mb-1"><strong>Date:</strong> <?= date('d/m/Y', strtotime($invoice['invoice_date'])) ?></div>
                <?php if (!empty($invoice['due_date'])): ?>
                    <div class="mb-1"><strong>Due Date:</strong> <?= date('d/m/Y', strtotime($invoice['due_date'])) ?></div>
                <?php endif; ?>
                <div class="mb-1">
                    <strong>Status:</strong>
                    <?php
                    $statusClass = [
                        'draft' => 'bg-secondary',
                        'sent' => 'bg-primary',
                        'paid' => 'bg-success',
                        'cancelled' => 'bg-danger'
                    ];
                    ?>
                    <span class="badge <?= $statusClass[$invoice['status']] ?? 'bg-secondary' ?>">
                        <?= ucfirst($invoice['status']) ?>
                    </span>
                </div>
            </div>
            <div class="col-md-6 text-md-end">
                <h5 class="mb-3">Subject:</h5>
                <p><?= $invoice['subject'] ?></p>
            </div>
        </div>

        <div class="table-responsive mb-4">
            <table class="table table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>Description</th>
                        <th class="text-end">Fee (₹)</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($items)): ?>
                        <tr>
                            <td colspan="2" class="text-center">No items found</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($items as $item): ?>
                            <tr>
                                <td><?= $item['description'] ?></td>
                                <td class="text-end"><?= number_format($item['fee'], 2) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
                <tfoot>
                    <tr>
                        <td class="text-end"><strong>Total:</strong></td>
                        <td class="text-end"><strong>₹<?= number_format($invoice['total'], 2) ?></strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>

        <?php if (!empty($invoice['notes'])): ?>
            <div class="mb-4">
                <h5>Notes:</h5>
                <p><?= nl2br($invoice['notes']) ?></p>
            </div>
        <?php endif; ?>

        <div class="mb-4">
            <div class="row">
                <div class="col-md-6">
                    <?php if (!empty($invoice['upi_id']) || !empty($invoice['qr_code'])): ?>
                        <h5>Payment Information:</h5>
                        <div class="d-flex">
                            <div class="me-4">
                                <?php if (!empty($invoice['upi_id'])): ?>
                                    <div class="mb-1"><strong>UPI ID:</strong> <?= $invoice['upi_id'] ?></div>
                                <?php endif; ?>
                            </div>
                            <?php if (!empty($invoice['qr_code'])): ?>
                                <div>
                                    <img src="<?= base_url('public/uploads/qr_codes/' . $invoice['qr_code']) ?>" alt="Payment QR Code" class="img-thumbnail" style="max-width: 150px;">
                                    <p class="mt-2">Scan to pay</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="col-md-6 text-md-end">
                    <h5>Signature:</h5>
                    <div>
                        <!-- Permanent signature image -->
                        <img src="<?= base_url('public/assets/signature.png') ?>" alt="Signature" class="img-thumbnail" style="max-height: 120px; max-width: 200px;">
                        <div class="mt-2">
                            <strong>KRISHNA RAM YADAV</strong><br>
                            Advocate
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-5">
            <p>Thank you!</p>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
