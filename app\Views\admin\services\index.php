<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-end mb-4">
    <a href="<?= site_url('services/new') ?>" class="btn btn-primary">Add New Service</a>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Title</th>
                        <th>Image</th>
                        <th>Status</th>
                        <th>Created At</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($services)): ?>
                        <tr>
                            <td colspan="6" class="text-center">No services found</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($services as $service): ?>
                            <tr>
                                <td><?= $service['id'] ?></td>
                                <td><?= $service['title'] ?></td>
                                <td>
                                    <?php if ($service['image']): ?>
                                        <img src="<?= base_url('public/uploads/services/' . $service['image']) ?>" alt="<?= $service['title'] ?>" width="50">
                                    <?php else: ?>
                                        No Image
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $service['status'] === 'active' ? 'success' : 'danger' ?>">
                                        <?= ucfirst($service['status']) ?>
                                    </span>
                                </td>
                                <td><?= date('M d, Y', strtotime($service['created_at'])) ?></td>
                                <td>
                                    <a href="<?= site_url('services/edit/' . $service['id']) ?>" class="btn btn-sm btn-primary">Edit</a>
                                    <a href="<?= site_url('services/delete/' . $service['id']) ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this service?')">Delete</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <?php if (!empty($pager)) : ?>
            <?= $pager->links('services', 'admin_full') ?>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>
