<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Edit Profile</h5>
            </div>
            <div class="card-body">
                <?php if (session()->has('errors')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <div class="d-flex">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <ul class="mb-0 ps-2">
                                <?php foreach (session('errors') as $error): ?>
                                    <li><?= $error ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <form action="<?= site_url('profile/update') ?>" method="post" enctype="multipart/form-data">
                    <div class="row mb-4">
                        <div class="col-md-3 text-center">
                            <?php
                            $imagePath = '';
                            if (!empty($user['profile_image'])) {
                                $imagePath = ROOTPATH . 'public/uploads/profile/' . $user['profile_image'];
                                log_message('debug', 'Edit profile - Image path: ' . $imagePath . ', Exists: ' . (file_exists($imagePath) ? 'Yes' : 'No'));
                            }

                            if (!empty($user['profile_image']) && file_exists($imagePath)):
                            ?>
                                <img src="<?= base_url('public/uploads/profile/' . $user['profile_image']) ?>" alt="<?= $user['name'] ?>" class="rounded-circle img-thumbnail mb-3" style="width: 150px; height: 150px; object-fit: cover;" id="profile-image-preview">
                            <?php else: ?>
                                <div class="avatar avatar-lg bg-primary text-white rounded-circle mx-auto mb-3" style="width: 150px; height: 150px; font-size: 3rem; display: flex; align-items: center; justify-content: center;" id="profile-image-placeholder">
                                    <?= substr($user['name'], 0, 1) ?>
                                </div>
                                <img src="" alt="" class="rounded-circle img-thumbnail mb-3 d-none" style="width: 150px; height: 150px; object-fit: cover;" id="profile-image-preview">
                            <?php endif; ?>

                            <div class="mb-3">
                                <label for="profile_image" class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-upload me-1"></i> Upload Photo
                                </label>
                                <input type="file" class="d-none" id="profile_image" name="profile_image" accept="image/*">
                            </div>
                            <small class="text-muted">Recommended: 300x300px</small>
                        </div>

                        <div class="col-md-9">
                            <div class="mb-3">
                                <label for="name" class="form-label">Full Name</label>
                                <input type="text" class="form-control" id="name" name="name" value="<?= old('name', $user['name']) ?>" required>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email" name="email" value="<?= old('email', $user['email']) ?>" required>
                            </div>

                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="text" class="form-control" id="phone" name="phone" value="<?= old('phone', $user['phone'] ?? '') ?>">
                            </div>

                            <div class="mb-3">
                                <label for="address" class="form-label">Address</label>
                                <input type="text" class="form-control" id="address" name="address" value="<?= old('address', $user['address'] ?? '') ?>">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="bio" class="form-label">Bio</label>
                        <textarea class="form-control" id="bio" name="bio" rows="4"><?= old('bio', $user['bio'] ?? '') ?></textarea>
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <a href="<?= site_url('profile') ?>" class="btn btn-outline-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">Save Changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Preview profile image before upload
    const profileImageInput = document.getElementById('profile_image');
    const profileImagePreview = document.getElementById('profile-image-preview');
    const profileImagePlaceholder = document.getElementById('profile-image-placeholder');

    profileImageInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                profileImagePreview.src = e.target.result;
                profileImagePreview.classList.remove('d-none');

                if (profileImagePlaceholder) {
                    profileImagePlaceholder.classList.add('d-none');
                }

                console.log('Image preview updated');
            }

            reader.readAsDataURL(this.files[0]);
            console.log('Reading file: ' + this.files[0].name);
        }
    });

    // Log form submission
    document.querySelector('form').addEventListener('submit', function() {
        console.log('Form submitted');
    });
});
</script>

<?= $this->endSection() ?>
