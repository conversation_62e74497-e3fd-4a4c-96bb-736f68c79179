<?php

namespace App\Controllers;

use App\Models\ServiceModel;
use App\Models\TeamModel;
use App\Models\GalleryModel;
use App\Models\ContactModel;
use App\Models\AboutModel;

class Search extends BaseController
{
    protected $serviceModel;
    protected $teamModel;
    protected $galleryModel;
    protected $contactModel;
    protected $aboutModel;

    public function __construct()
    {
        $this->serviceModel = new ServiceModel();
        $this->teamModel = new TeamModel();
        $this->galleryModel = new GalleryModel();
        $this->contactModel = new ContactModel();
        $this->aboutModel = new AboutModel();
    }

    public function index()
    {
        $query = $this->request->getGet('q');
        
        if (empty($query)) {
            return redirect()->back();
        }

        // Search in services
        $services = $this->serviceModel->like('title', $query)
                                      ->orLike('description', $query)
                                      ->findAll();

        // Search in team
        $team = $this->teamModel->like('name', $query)
                               ->orLike('position', $query)
                               ->orLike('bio', $query)
                               ->findAll();

        // Search in gallery
        $gallery = $this->galleryModel->like('title', $query)
                                     ->orLike('description', $query)
                                     ->findAll();

        // Search in contact messages
        $contact = $this->contactModel->like('name', $query)
                                     ->orLike('email', $query)
                                     ->orLike('subject', $query)
                                     ->orLike('message', $query)
                                     ->findAll();

        // Search in about
        $about = $this->aboutModel->like('title', $query)
                                 ->orLike('content', $query)
                                 ->orLike('mission', $query)
                                 ->orLike('vision', $query)
                                 ->findAll();

        $data = [
            'title'    => 'Search Results: ' . $query,
            'query'    => $query,
            'services' => $services,
            'team'     => $team,
            'gallery'  => $gallery,
            'contact'  => $contact,
            'about'    => $about,
            'total'    => count($services) + count($team) + count($gallery) + count($contact) + count($about),
        ];

        return view('admin/search/results', $data);
    }

    public function ajax()
    {
        $query = $this->request->getGet('q');
        
        if (empty($query)) {
            return $this->response->setJSON(['results' => []]);
        }

        // Search in services
        $services = $this->serviceModel->like('title', $query)
                                      ->orLike('description', $query)
                                      ->findAll(5);

        // Search in team
        $team = $this->teamModel->like('name', $query)
                               ->orLike('position', $query)
                               ->findAll(5);

        // Search in gallery
        $gallery = $this->galleryModel->like('title', $query)
                                     ->orLike('description', $query)
                                     ->findAll(5);

        // Format results
        $results = [];

        foreach ($services as $service) {
            $results[] = [
                'title' => $service['title'],
                'url'   => site_url('services/edit/' . $service['id']),
                'type'  => 'Service',
                'icon'  => 'bi-gear',
            ];
        }

        foreach ($team as $member) {
            $results[] = [
                'title' => $member['name'],
                'url'   => site_url('team/edit/' . $member['id']),
                'type'  => 'Team Member',
                'icon'  => 'bi-people',
            ];
        }

        foreach ($gallery as $item) {
            $results[] = [
                'title' => $item['title'],
                'url'   => site_url('gallery/edit/' . $item['id']),
                'type'  => 'Gallery Item',
                'icon'  => 'bi-images',
            ];
        }

        return $this->response->setJSON(['results' => $results]);
    }
}
