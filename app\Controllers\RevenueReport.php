<?php

namespace App\Controllers;

use App\Models\MonthlyRevenueModel;
use App\Models\InvoiceModel;

class RevenueReport extends BaseController
{
    protected $monthlyRevenueModel;
    protected $invoiceModel;

    public function __construct()
    {
        $this->monthlyRevenueModel = new MonthlyRevenueModel();
        $this->invoiceModel = new InvoiceModel();
    }

    public function index()
    {
        // Get current year and available years for the dropdown
        $currentYear = date('Y');
        $years = $this->getAvailableYears();

        // Get selected year from query params or use current year
        $selectedYear = $this->request->getGet('year') ?? $currentYear;

        // Check if we need to calculate the current month's revenue
        $currentMonth = date('m');
        if ($selectedYear == $currentYear) {
            // Check if we already have data for the current month
            $monthlyRevenue = $this->monthlyRevenueModel->getMonthlyRevenue($currentYear, $currentMonth);

            // If no data exists or if it's a refresh request, calculate it
            if (!$monthlyRevenue || $this->request->getGet('refresh') === 'true') {
                $this->monthlyRevenueModel->calculateCurrentMonthRevenue();
            }
        }

        // Get monthly revenue data for the selected year
        $monthlyData = $this->monthlyRevenueModel->getYearlyRevenue($selectedYear);

        // Prepare data for the chart
        $chartData = $this->prepareChartData($monthlyData);

        $lastUpdated = date('Y-m-d H:i:s');

        // Check if this is an AJAX request
        if ($this->request->getGet('ajax') === 'true') {
            // Generate table HTML for the response
            $tableHtml = $this->generateTableHtml($monthlyData, $selectedYear);

            // Return JSON response
            return $this->response->setJSON([
                'success' => true,
                'chartData' => $chartData,
                'tableHtml' => $tableHtml,
                'lastUpdated' => date('M d, Y h:i A', strtotime($lastUpdated))
            ]);
        }

        // Regular view response
        $data = [
            'title'       => 'Revenue Reports',
            'years'       => $years,
            'selectedYear' => $selectedYear,
            'monthlyData' => $monthlyData,
            'chartData'   => $chartData,
            'currentMonth' => $currentMonth,
            'currentYear' => $currentYear,
            'lastUpdated' => $lastUpdated,
        ];

        return view('admin/reports/revenue', $data);
    }

    public function monthly($year = null, $month = null)
    {
        // If year or month not provided, use current
        if (!$year || !$month) {
            $year = date('Y');
            $month = date('m');
        }

        // Get current year and month
        $currentYear = date('Y');
        $currentMonth = date('m');
        $isCurrentMonth = ($year == $currentYear && $month == $currentMonth);

        // Get monthly revenue data
        $monthlyRevenue = $this->monthlyRevenueModel->getMonthlyRevenue($year, $month);

        // If no data exists or if it's a refresh request for the current month, calculate it
        if (!$monthlyRevenue || ($isCurrentMonth && $this->request->getGet('refresh') === 'true')) {
            if ($isCurrentMonth) {
                $this->monthlyRevenueModel->calculateCurrentMonthRevenue();
            } else {
                // For past months, calculate if data doesn't exist
                if (!$monthlyRevenue) {
                    $startDate = "$year-$month-01";
                    $endDate = date('Y-m-t', strtotime($startDate));
                    $stats = $this->invoiceModel->getInvoiceStatusStats($startDate, $endDate);

                    $data = [
                        'total_revenue' => $stats['paid']['amount'],
                        'draft_count' => $stats['draft']['count'],
                        'draft_amount' => $stats['draft']['amount'],
                        'sent_count' => $stats['sent']['count'],
                        'sent_amount' => $stats['sent']['amount'],
                        'paid_count' => $stats['paid']['count'],
                        'paid_amount' => $stats['paid']['amount'],
                        'cancelled_count' => $stats['cancelled']['count'],
                        'cancelled_amount' => $stats['cancelled']['amount'],
                    ];

                    $this->monthlyRevenueModel->saveMonthlyRevenue($year, $month, $data);
                }
            }

            $monthlyRevenue = $this->monthlyRevenueModel->getMonthlyRevenue($year, $month);
        }

        // Get daily revenue data for the month
        $startDate = "$year-$month-01";
        $endDate = date('Y-m-t', strtotime($startDate));
        $dailyRevenue = $this->getDailyRevenue($startDate, $endDate);

        $lastUpdated = date('Y-m-d H:i:s');

        // Check if this is an AJAX request
        if ($this->request->getGet('ajax') === 'true') {
            // Generate daily revenue table HTML
            $dailyTableHtml = $this->generateDailyTableHtml($dailyRevenue);

            // Return JSON response
            return $this->response->setJSON([
                'success' => true,
                'monthlyRevenue' => $monthlyRevenue,
                'dailyRevenue' => $dailyRevenue,
                'dailyTableHtml' => $dailyTableHtml,
                'lastUpdated' => date('M d, Y h:i A', strtotime($lastUpdated))
            ]);
        }

        $data = [
            'title'          => 'Monthly Revenue Report: ' . date('F Y', strtotime($startDate)),
            'monthlyRevenue' => $monthlyRevenue,
            'dailyRevenue'   => $dailyRevenue,
            'year'           => $year,
            'month'          => $month,
            'currentYear'    => $currentYear,
            'currentMonth'   => $currentMonth,
            'lastUpdated'    => $lastUpdated,
        ];

        return view('admin/reports/monthly_revenue', $data);
    }

    public function calculate()
    {
        // Calculate current month's revenue
        $result = $this->monthlyRevenueModel->calculateCurrentMonthRevenue();

        if ($result) {
            return redirect()->to('revenue-reports')->with('message', 'Monthly revenue calculated successfully.');
        } else {
            return redirect()->to('revenue-reports')->with('error', 'Failed to calculate monthly revenue.');
        }
    }

    /**
     * Get available years for revenue reports
     *
     * @return array
     */
    private function getAvailableYears()
    {
        $years = [];
        $currentYear = date('Y');

        // Get years from database
        $db = \Config\Database::connect();
        $query = $db->query("SELECT DISTINCT year FROM monthly_revenue ORDER BY year DESC");
        $dbYears = array_column($query->getResultArray(), 'year');

        // If no years in database, use current year
        if (empty($dbYears)) {
            $years = [$currentYear];
        } else {
            $years = $dbYears;

            // Add current year if not in the list
            if (!in_array($currentYear, $years)) {
                array_unshift($years, $currentYear);
            }
        }

        return $years;
    }

    /**
     * Prepare chart data from monthly revenue data
     *
     * @param array $monthlyData
     * @return array
     */
    private function prepareChartData($monthlyData)
    {
        $months = [];
        $revenue = [];
        $draftAmount = [];
        $sentAmount = [];
        $paidAmount = [];
        $cancelledAmount = [];

        // Initialize arrays with zeros for all months
        for ($i = 1; $i <= 12; $i++) {
            $monthName = date('M', mktime(0, 0, 0, $i, 1));
            $months[] = $monthName;
            $revenue[$i] = 0;
            $draftAmount[$i] = 0;
            $sentAmount[$i] = 0;
            $paidAmount[$i] = 0;
            $cancelledAmount[$i] = 0;
        }

        // Fill in actual data
        foreach ($monthlyData as $data) {
            $monthNum = (int)$data['month'];
            $revenue[$monthNum] = (float)$data['total_revenue'];
            $draftAmount[$monthNum] = (float)$data['draft_amount'];
            $sentAmount[$monthNum] = (float)$data['sent_amount'];
            $paidAmount[$monthNum] = (float)$data['paid_amount'];
            $cancelledAmount[$monthNum] = (float)$data['cancelled_amount'];
        }

        return [
            'months' => $months,
            'revenue' => array_values($revenue),
            'draftAmount' => array_values($draftAmount),
            'sentAmount' => array_values($sentAmount),
            'paidAmount' => array_values($paidAmount),
            'cancelledAmount' => array_values($cancelledAmount),
        ];
    }

    /**
     * Generate HTML for the monthly revenue table
     *
     * @param array $monthlyData
     * @param int $selectedYear
     * @return string
     */
    private function generateTableHtml($monthlyData, $selectedYear)
    {
        $monthNames = [
            1 => 'January', 2 => 'February', 3 => 'March', 4 => 'April',
            5 => 'May', 6 => 'June', 7 => 'July', 8 => 'August',
            9 => 'September', 10 => 'October', 11 => 'November', 12 => 'December'
        ];

        // Create an array with all months
        $allMonths = [];
        foreach ($monthNames as $num => $name) {
            $allMonths[$num] = [
                'month' => $num,
                'month_name' => $name,
                'total_revenue' => 0,
                'paid_count' => 0,
                'paid_amount' => 0,
                'sent_count' => 0,
                'sent_amount' => 0,
                'draft_count' => 0,
                'draft_amount' => 0,
                'cancelled_count' => 0,
                'cancelled_amount' => 0,
                'has_data' => false
            ];
        }

        // Fill in actual data
        foreach ($monthlyData as $data) {
            $monthNum = (int)$data['month'];
            $allMonths[$monthNum] = [
                'month' => $monthNum,
                'month_name' => $monthNames[$monthNum],
                'total_revenue' => $data['total_revenue'],
                'paid_count' => $data['paid_count'],
                'paid_amount' => $data['paid_amount'],
                'sent_count' => $data['sent_count'],
                'sent_amount' => $data['sent_amount'],
                'draft_count' => $data['draft_count'],
                'draft_amount' => $data['draft_amount'],
                'cancelled_count' => $data['cancelled_count'],
                'cancelled_amount' => $data['cancelled_amount'],
                'has_data' => true
            ];
        }

        // Sort by month (descending)
        krsort($allMonths);

        // Generate HTML
        $html = '';
        foreach ($allMonths as $monthData) {
            $monthNum = $monthData['month'];
            $html .= '<tr>';
            $html .= '<td>' . $monthData['month_name'] . '</td>';
            $html .= '<td>₹' . number_format($monthData['total_revenue'], 2) . '</td>';
            $html .= '<td>';
            $html .= '<span class="badge bg-success-subtle text-success">' . $monthData['paid_count'] . '</span>';
            $html .= '<span class="ms-2">₹' . number_format($monthData['paid_amount'], 2) . '</span>';
            $html .= '</td>';
            $html .= '<td>';
            $html .= '<span class="badge bg-info-subtle text-info">' . $monthData['sent_count'] . '</span>';
            $html .= '<span class="ms-2">₹' . number_format($monthData['sent_amount'], 2) . '</span>';
            $html .= '</td>';
            $html .= '<td>';
            $html .= '<span class="badge bg-secondary-subtle text-secondary">' . $monthData['draft_count'] . '</span>';
            $html .= '<span class="ms-2">₹' . number_format($monthData['draft_amount'], 2) . '</span>';
            $html .= '</td>';
            $html .= '<td>';
            $html .= '<span class="badge bg-danger-subtle text-danger">' . $monthData['cancelled_count'] . '</span>';
            $html .= '<span class="ms-2">₹' . number_format($monthData['cancelled_amount'], 2) . '</span>';
            $html .= '</td>';
            $html .= '<td>';
            $html .= '<a href="' . site_url("revenue-reports/monthly/{$selectedYear}/{$monthNum}") . '" class="btn btn-sm btn-outline-primary">';
            $html .= '<i class="bi bi-eye"></i> Details';
            $html .= '</a>';
            $html .= '</td>';
            $html .= '</tr>';
        }

        return $html;
    }

    /**
     * Generate HTML for the daily revenue table
     *
     * @param array $dailyRevenue
     * @return string
     */
    private function generateDailyTableHtml($dailyRevenue)
    {
        if (empty($dailyRevenue)) {
            return '<div class="text-center py-4"><p class="text-muted">No revenue data available for this month.</p></div>';
        }

        $html = '<div class="table-responsive"><table class="table table-hover">';
        $html .= '<thead class="table-light"><tr><th>Date</th><th>Revenue</th><th>Paid Invoices</th></tr></thead>';
        $html .= '<tbody>';

        foreach ($dailyRevenue as $day) {
            $html .= '<tr>';
            $html .= '<td>' . date('d M Y (D)', strtotime($day['date'])) . '</td>';
            $html .= '<td>₹' . number_format($day['paid_amount'], 2) . '</td>';
            $html .= '<td><span class="badge bg-success">' . $day['paid_count'] . '</span></td>';
            $html .= '</tr>';
        }

        $html .= '</tbody></table></div>';

        return $html;
    }

    /**
     * Get daily revenue data for a date range
     *
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    private function getDailyRevenue($startDate, $endDate)
    {
        $db = \Config\Database::connect();
        $query = $db->query("
            SELECT
                DATE(invoice_date) as date,
                SUM(CASE WHEN status = 'paid' THEN total ELSE 0 END) as paid_amount,
                COUNT(CASE WHEN status = 'paid' THEN 1 ELSE NULL END) as paid_count
            FROM
                invoices
            WHERE
                invoice_date BETWEEN '$startDate' AND '$endDate'
            GROUP BY
                DATE(invoice_date)
            ORDER BY
                date ASC
        ");

        return $query->getResultArray();
    }
}
