<?php

namespace App\Controllers;

use App\Models\AboutImageModel;
use CodeIgniter\Exceptions\PageNotFoundException;

class AboutImage extends BaseController
{
    protected $aboutImageModel;

    public function __construct()
    {
        $this->aboutImageModel = new AboutImageModel();
    }

    public function index()
    {
        $data = [
            'title'  => 'Manage About Images',
            'images' => $this->aboutImageModel->orderBy('order', 'ASC')->paginate(10, 'about_images'),
            'pager'  => $this->aboutImageModel->pager,
        ];

        return view('admin/about/images/index', $data);
    }

    public function new()
    {
        $data = [
            'title' => 'Add New About Image',
        ];

        return view('admin/about/images/create', $data);
    }

    public function create()
    {
        if (!$this->validate($this->aboutImageModel->validationRules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Handle file upload
        $image = $this->request->getFile('image');
        $imageName = null;

        if ($image && $image->isValid() && !$image->hasMoved()) {
            $imageName = $image->getRandomName();
            $image->move(ROOTPATH . 'public/uploads/about', $imageName);
        }

        $data = [
            'image'  => $imageName,
            'alt'    => $this->request->getPost('alt'),
            'order'  => $this->request->getPost('order'),
            'status' => $this->request->getPost('status'),
        ];

        $this->aboutImageModel->insert($data);

        return redirect()->to('/about-images')->with('success', 'About image added successfully');
    }

    public function edit($id = null)
    {
        $image = $this->aboutImageModel->find($id);

        if (!$image) {
            throw new PageNotFoundException('About image not found');
        }

        $data = [
            'title' => 'Edit About Image',
            'image' => $image,
        ];

        return view('admin/about/images/edit', $data);
    }

    public function update($id = null)
    {
        $aboutImage = $this->aboutImageModel->find($id);

        if (!$aboutImage) {
            throw new PageNotFoundException('About image not found');
        }

        // Custom validation rules for update (image is optional)
        $rules = [
            'alt'    => 'permit_empty|max_length[200]',
            'order'  => 'permit_empty|numeric',
            'status' => 'required|in_list[active,inactive]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Handle file upload
        $image = $this->request->getFile('image');
        $imageName = $aboutImage['image'];

        if ($image && $image->isValid() && !$image->hasMoved()) {
            // Delete old image if exists
            if ($aboutImage['image'] && file_exists(ROOTPATH . 'public/uploads/about/' . $aboutImage['image'])) {
                unlink(ROOTPATH . 'public/uploads/about/' . $aboutImage['image']);
            }

            $imageName = $image->getRandomName();
            $image->move(ROOTPATH . 'public/uploads/about', $imageName);
        }

        $data = [
            'image'  => $imageName,
            'alt'    => $this->request->getPost('alt'),
            'order'  => $this->request->getPost('order'),
            'status' => $this->request->getPost('status'),
        ];

        $this->aboutImageModel->update($id, $data);

        return redirect()->to('/about-images')->with('success', 'About image updated successfully');
    }

    public function delete($id = null)
    {
        $aboutImage = $this->aboutImageModel->find($id);

        if (!$aboutImage) {
            throw new PageNotFoundException('About image not found');
        }

        // Delete image file if exists
        if ($aboutImage['image'] && file_exists(ROOTPATH . 'public/uploads/about/' . $aboutImage['image'])) {
            unlink(ROOTPATH . 'public/uploads/about/' . $aboutImage['image']);
        }

        $this->aboutImageModel->delete($id);

        return redirect()->to('/about-images')->with('success', 'About image deleted successfully');
    }
}
