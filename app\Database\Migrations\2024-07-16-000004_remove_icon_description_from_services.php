<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class RemoveIconDescriptionFromServices extends Migration
{
    public function up()
    {
        // Drop the icon and description columns from the services table
        $this->forge->dropColumn('services', 'icon');
        $this->forge->dropColumn('services', 'description');
    }

    public function down()
    {
        // Add back the icon and description columns if needed
        $fields = [
            'icon' => [
                'type'       => 'VARCHAR',
                'constraint' => '100',
                'null'       => true,
                'after'      => 'title'
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
                'after' => 'icon'
            ],
        ];
        
        $this->forge->addColumn('services', $fields);
    }
}
