<?php

namespace App\Controllers;

use App\Models\ContactModel;
use CodeIgniter\Exceptions\PageNotFoundException;

class Contact extends BaseController
{
    protected $contactModel;

    public function __construct()
    {
        $this->contactModel = new ContactModel();
    }

    public function index()
    {
        $data = [
            'title'    => 'Contact Messages',
            'messages' => $this->contactModel->orderBy('id', 'DESC')->paginate(10, 'messages'),
            'pager'    => $this->contactModel->pager,
        ];

        return view('admin/contact/index', $data);
    }

    public function view($id = null)
    {
        $message = $this->contactModel->find($id);

        if (!$message) {
            throw new PageNotFoundException('Message not found');
        }

        // Mark as read
        if ($message['status'] === 'unread') {
            $this->contactModel->update($id, ['status' => 'read']);
        }

        $data = [
            'title'   => 'View Message',
            'message' => $message,
        ];

        return view('admin/contact/view', $data);
    }

    public function delete($id = null)
    {
        $message = $this->contactModel->find($id);

        if (!$message) {
            throw new PageNotFoundException('Message not found');
        }

        $this->contactModel->delete($id);
        return redirect()->to('/contact')->with('message', 'Message deleted successfully');
    }

    // Frontend contact form submission
    public function submit()
    {
        if (!$this->validate($this->contactModel->validationRules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name'    => $this->request->getPost('name'),
            'email'   => $this->request->getPost('email'),
            'subject' => $this->request->getPost('subject'),
            'message' => $this->request->getPost('message'),
            'status'  => 'unread',
        ];

        $id = $this->contactModel->insert($data);

        // Create notification for all admin users
        $userModel = new \App\Models\UserModel();
        $admins = $userModel->where('role', 'admin')->findAll();

        foreach ($admins as $admin) {
            create_notification(
                $admin['id'],
                'message',
                'New Message Received',
                'New message from ' . $data['name'] . ': ' . $data['subject'],
                site_url('contact/view/' . $id),
                'bi-envelope'
            );
        }

        return redirect()->back()->with('message', 'Your message has been sent successfully. We will get back to you soon.');
    }
}
