<?php

namespace App\Controllers;

use CodeIgniter\HTTP\ResponseInterface;

class CKEditorUpload extends BaseController
{
    /**
     * Handle CKEditor 5 image uploads
     *
     * @return ResponseInterface
     */
    public function upload()
    {
        // Set appropriate headers for CORS if needed
        $this->response->setHeader('Access-Control-Allow-Origin', '*');
        $this->response->setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
        $this->response->setHeader('Access-Control-Allow-Headers', 'Content-Type, X-Requested-With, X-CSRF-TOKEN');

        // <PERSON>le preflight OPTIONS request
        if (strtolower($this->request->getMethod()) === 'options') {
            return $this->response->setStatusCode(200);
        }

        // Check if this is a POST request
        if (strtolower($this->request->getMethod()) !== 'post') {
            return $this->response->setJSON([
                'uploaded' => false,
                'error' => [
                    'message' => 'Invalid request method: ' . $this->request->getMethod()
                ]
            ])->setStatusCode(400);
        }

        // Log the request for debugging
        log_message('debug', 'CKEditor upload request: Method=' . $this->request->getMethod() . ', Files=' . json_encode($_FILES));

        // Get the uploaded file
        $image = $this->request->getFile('upload');

        // Check if file exists and is valid
        if (!$image || !$image->isValid() || $image->hasMoved()) {
            $errorMsg = 'No valid file uploaded';
            if (!$image) {
                $errorMsg .= ' - File not found in request';
            } elseif (!$image->isValid()) {
                $errorMsg .= ' - File is not valid: ' . $image->getErrorString();
            } elseif ($image->hasMoved()) {
                $errorMsg .= ' - File has already been moved';
            }

            log_message('error', 'CKEditor upload error: ' . $errorMsg);

            return $this->response->setJSON([
                'uploaded' => false,
                'error' => [
                    'message' => $errorMsg
                ]
            ])->setStatusCode(400);
        }

        // Log the file details
        if ($image) {
            log_message('debug', 'CKEditor upload file details: Name=' . $image->getName() .
                ', Size=' . $image->getSize() .
                ', Type=' . $image->getMimeType() .
                ', Error=' . $image->getError());
        }

        // Skip validation for now to debug the issue
        /*
        $validationRules = [
            'upload' => [
                'label' => 'Image',
                'rules' => 'uploaded[upload]|is_image[upload]|mime_in[upload,image/jpg,image/jpeg,image/png,image/gif]|max_size[upload,5120]',
            ],
        ];

        if (!$this->validate($validationRules)) {
            $errorMsg = implode(', ', $this->validator->getErrors());
            log_message('error', 'CKEditor upload validation error: ' . $errorMsg);

            return $this->response->setJSON([
                'uploaded' => false,
                'error' => [
                    'message' => $errorMsg
                ]
            ])->setStatusCode(400);
        }
        */

        // Create upload directory if it doesn't exist
        $uploadPath = ROOTPATH . 'public/uploads/ckeditor';
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0777, true);
        }

        // Generate a unique filename
        $newName = $image->getRandomName();

        // Move the file to the upload directory
        try {
            $image->move($uploadPath, $newName);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'uploaded' => false,
                'error' => [
                    'message' => 'Failed to save the uploaded file: ' . $e->getMessage()
                ]
            ])->setStatusCode(500);
        }

        // Generate the URL for the uploaded file
        $fileUrl = base_url('public/uploads/ckeditor/' . $newName);

        // Log success
        log_message('info', 'CKEditor upload successful: ' . $fileUrl);

        // Return success response with URL
        return $this->response->setJSON([
            'uploaded' => true,
            'url' => $fileUrl
        ]);
    }
}
