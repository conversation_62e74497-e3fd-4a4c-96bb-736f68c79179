<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\BlogCommentModel;
use App\Models\BlogPostModel;

class BlogComment extends BaseController
{
    protected $blogCommentModel;
    protected $blogPostModel;

    public function __construct()
    {
        $this->blogCommentModel = new BlogCommentModel();
        $this->blogPostModel = new BlogPostModel();

        // Load text helper for character_limiter function
        helper('text');
    }

    public function index()
    {
        $status = $this->request->getGet('status');
        $query = $this->blogCommentModel->select('blog_comments.*, blog_posts.title as post_title')
            ->join('blog_posts', 'blog_posts.id = blog_comments.post_id');

        if ($status && in_array($status, ['pending', 'approved', 'spam'])) {
            $query->where('blog_comments.status', $status);
        }

        $data = [
            'title'    => 'Manage Comments',
            'comments' => $query->orderBy('blog_comments.created_at', 'DESC')
                ->paginate(10, 'comments'),
            'pager'    => $this->blogCommentModel->pager,
            'status'   => $status,
        ];

        return view('admin/blog/comments/all', $data);
    }

    public function edit($id = null)
    {
        $comment = $this->blogCommentModel->find($id);

        if (!$comment) {
            return redirect()->to('blog-comments')->with('error', 'Comment not found.');
        }

        $post = $this->blogPostModel->find($comment['post_id']);

        $data = [
            'title'   => 'Edit Comment',
            'comment' => $comment,
            'post'    => $post,
        ];

        return view('admin/blog/comments/edit', $data);
    }

    public function update($id = null)
    {
        $comment = $this->blogCommentModel->find($id);

        if (!$comment) {
            return redirect()->to('blog-comments')->with('error', 'Comment not found.');
        }

        $rules = [
            'name'    => 'required|min_length[3]|max_length[100]',
            'email'   => 'required|valid_email|max_length[100]',
            'website' => 'permit_empty|valid_url_strict|max_length[255]',
            'comment' => 'required|min_length[5]',
            'status'  => 'required|in_list[pending,approved,spam]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name'    => $this->request->getPost('name'),
            'email'   => $this->request->getPost('email'),
            'website' => $this->request->getPost('website'),
            'comment' => $this->request->getPost('comment'),
            'status'  => $this->request->getPost('status'),
        ];

        if ($this->blogCommentModel->update($id, $data)) {
            return redirect()->to('blog-comments')->with('success', 'Comment updated successfully.');
        }

        return redirect()->back()->withInput()->with('error', 'Failed to update comment. Please try again.');
    }

    public function delete($id = null)
    {
        $comment = $this->blogCommentModel->find($id);

        if (!$comment) {
            return redirect()->to('blog-comments')->with('error', 'Comment not found.');
        }

        if ($this->blogCommentModel->delete($id)) {
            return redirect()->to('blog-comments')->with('success', 'Comment deleted successfully.');
        }

        return redirect()->to('blog-comments')->with('error', 'Failed to delete comment. Please try again.');
    }

    /**
     * Update comment status
     */
    public function updateStatus($id = null)
    {
        $comment = $this->blogCommentModel->find($id);

        if (!$comment) {
            return $this->response->setJSON(['success' => false, 'message' => 'Comment not found.']);
        }

        $status = $this->request->getPost('status');

        if (!in_array($status, ['pending', 'approved', 'spam'])) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid status.']);
        }

        if ($this->blogCommentModel->update($id, ['status' => $status])) {
            return $this->response->setJSON(['success' => true, 'message' => 'Comment status updated successfully.']);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to update comment status.']);
    }

    /**
     * Bulk action for comments
     */
    public function bulkAction()
    {
        $action = $this->request->getPost('action');
        $ids = $this->request->getPost('ids');

        if (empty($ids)) {
            return redirect()->back()->with('error', 'No comments selected.');
        }

        if ($action === 'delete') {
            $this->blogCommentModel->delete($ids);
            return redirect()->back()->with('success', 'Selected comments deleted successfully.');
        } elseif (in_array($action, ['pending', 'approved', 'spam'])) {
            foreach ($ids as $id) {
                $this->blogCommentModel->update($id, ['status' => $action]);
            }
            return redirect()->back()->with('success', 'Selected comments updated successfully.');
        }

        return redirect()->back()->with('error', 'Invalid action.');
    }
}
