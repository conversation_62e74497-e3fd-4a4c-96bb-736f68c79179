<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;
use App\Models\UserModel;

class AdminSeeder extends Seeder
{
    public function run()
    {
        $userModel = new UserModel();
        
        // Check if admin user already exists
        $existingAdmin = $userModel->where('email', '<EMAIL>')->first();
        
        if (!$existingAdmin) {
            $data = [
                'name'     => 'Admin',
                'email'    => '<EMAIL>',
                'password' => 'password',
            ];
            
            $userModel->insert($data);
            
            echo "Admin user created successfully.\n";
        } else {
            echo "Admin user already exists.\n";
        }
    }
}
