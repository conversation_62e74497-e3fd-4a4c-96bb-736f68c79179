<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class RemoveBankingDetailsAddSignature extends Migration
{
    public function up()
    {
        // Drop the banking details columns
        $this->forge->dropColumn('invoices', 'bar_council_no');
        $this->forge->dropColumn('invoices', 'pan_no');
        $this->forge->dropColumn('invoices', 'bank_name');
        $this->forge->dropColumn('invoices', 'bank_account_no');
        $this->forge->dropColumn('invoices', 'ifsc_code');
        
        // Add signature image column
        $fields = [
            'signature_image' => [
                'type' => 'VARCHAR',
                'constraint' => '255',
                'null' => true,
                'after' => 'qr_code'
            ],
        ];
        
        $this->forge->addColumn('invoices', $fields);
    }

    public function down()
    {
        // Add back the banking details columns
        $fields = [
            'bar_council_no' => [
                'type' => 'VARCHAR',
                'constraint' => '50',
                'null' => true,
            ],
            'pan_no' => [
                'type' => 'VARCHAR',
                'constraint' => '50',
                'null' => true,
            ],
            'bank_name' => [
                'type' => 'VARCHAR',
                'constraint' => '100',
                'null' => true,
            ],
            'bank_account_no' => [
                'type' => 'VARCHAR',
                'constraint' => '50',
                'null' => true,
            ],
            'ifsc_code' => [
                'type' => 'VARCHAR',
                'constraint' => '20',
                'null' => true,
            ],
        ];
        
        $this->forge->addColumn('invoices', $fields);
        
        // Drop the signature image column
        $this->forge->dropColumn('invoices', 'signature_image');
    }
}
