<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-end mb-4">
    <a href="<?= site_url('team/new') ?>" class="btn btn-primary">Add New Team Member</a>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Position</th>
                        <th>Image</th>
                        <th>Status</th>
                        <th>Created At</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($team)): ?>
                        <tr>
                            <td colspan="7" class="text-center">No team members found</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($team as $member): ?>
                            <tr>
                                <td><?= $member['id'] ?></td>
                                <td><?= $member['name'] ?></td>
                                <td><?= $member['position'] ?></td>
                                <td>
                                    <?php if ($member['image']): ?>
                                        <img src="<?= base_url('public/uploads/team/' . $member['image']) ?>" alt="<?= $member['name'] ?>" width="50">
                                    <?php else: ?>
                                        No Image
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $member['status'] === 'active' ? 'success' : 'danger' ?>">
                                        <?= ucfirst($member['status']) ?>
                                    </span>
                                </td>
                                <td><?= date('M d, Y', strtotime($member['created_at'])) ?></td>
                                <td>
                                    <a href="<?= site_url('team/edit/' . $member['id']) ?>" class="btn btn-sm btn-primary">Edit</a>
                                    <a href="<?= site_url('team/delete/' . $member['id']) ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this team member?')">Delete</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <?php if (!empty($pager)) : ?>
            <?= $pager->links('team', 'admin_full') ?>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>
