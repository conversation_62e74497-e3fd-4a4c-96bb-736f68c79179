<?php

namespace App\Controllers;

use App\Models\ServiceModel;
use CodeIgniter\Exceptions\PageNotFoundException;

class Services extends BaseController
{
    protected $serviceModel;

    public function __construct()
    {
        $this->serviceModel = new ServiceModel();
    }

    public function index()
    {
        $data = [
            'title'    => 'Manage Services',
            'services' => $this->serviceModel->orderBy('id', 'DESC')->paginate(10, 'services'),
            'pager'    => $this->serviceModel->pager,
        ];

        return view('admin/services/index', $data);
    }

    public function new()
    {
        $data = [
            'title' => 'Add New Service',
        ];

        return view('admin/services/create', $data);
    }

    public function create()
    {
        if (!$this->validate($this->serviceModel->validationRules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Handle file upload
        $image = $this->request->getFile('image');
        $imageName = null;

        if ($image && $image->isValid() && !$image->hasMoved()) {
            $imageName = $image->getRandomName();
            $image->move(ROOTPATH . 'public/uploads/services', $imageName);
        }

        $data = [
            'title'  => $this->request->getPost('title'),
            'image'  => $imageName,
            'status' => $this->request->getPost('status'),
        ];

        $id = $this->serviceModel->insert($data);

        // Create notification for the current user
        $userId = session()->get('id');
        create_notification(
            $userId,
            'service',
            'New Service Added',
            'Service "' . $data['title'] . '" has been added',
            site_url('services/edit/' . $id),
            'bi-gear'
        );

        return redirect()->to('/services')->with('message', 'Service added successfully');
    }

    public function edit($id = null)
    {
        $service = $this->serviceModel->find($id);

        if (!$service) {
            throw new PageNotFoundException('Service not found');
        }

        $data = [
            'title'   => 'Edit Service',
            'service' => $service,
        ];

        return view('admin/services/edit', $data);
    }

    public function update($id = null)
    {
        $service = $this->serviceModel->find($id);

        if (!$service) {
            throw new PageNotFoundException('Service not found');
        }

        if (!$this->validate($this->serviceModel->validationRules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Handle file upload
        $image = $this->request->getFile('image');
        $imageName = $service['image'];

        if ($image && $image->isValid() && !$image->hasMoved()) {
            // Delete old image if exists
            if ($service['image'] && file_exists(ROOTPATH . 'public/uploads/services/' . $service['image'])) {
                unlink(ROOTPATH . 'public/uploads/services/' . $service['image']);
            }

            $imageName = $image->getRandomName();
            $image->move(ROOTPATH . 'public/uploads/services', $imageName);
        }

        $data = [
            'title'  => $this->request->getPost('title'),
            'image'  => $imageName,
            'status' => $this->request->getPost('status'),
        ];

        $this->serviceModel->update($id, $data);
        return redirect()->to('/services')->with('message', 'Service updated successfully');
    }

    public function delete($id = null)
    {
        $service = $this->serviceModel->find($id);

        if (!$service) {
            throw new PageNotFoundException('Service not found');
        }

        // Delete image if exists
        if ($service['image'] && file_exists(ROOTPATH . 'public/uploads/services/' . $service['image'])) {
            unlink(ROOTPATH . 'public/uploads/services/' . $service['image']);
        }

        $this->serviceModel->delete($id);
        return redirect()->to('/services')->with('message', 'Service deleted successfully');
    }
}
