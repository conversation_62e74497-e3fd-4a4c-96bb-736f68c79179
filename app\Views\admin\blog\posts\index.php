<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-end align-items-center mb-4">
    <a href="<?= site_url('blog-posts/new') ?>" class="btn btn-primary">
        <i class="bi bi-plus-circle me-1"></i> Add New Post
    </a>
</div>

<?= $this->include('admin/partials/alerts') ?>

<div class="card">
    <div class="card-body">
        <?php if (empty($posts)): ?>
            <div class="text-center py-5">
                <div class="mb-3">
                    <i class="bi bi-file-earmark-text fs-1 text-muted"></i>
                </div>
                <h5>No Posts Found</h5>
                <p class="text-muted">Get started by creating a new blog post.</p>
                <a href="<?= site_url('blog-posts/new') ?>" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-1"></i> Add New Post
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead>
                        <tr>
                            <th width="50">#</th>
                            <th>Title</th>
                            <th>Category</th>
                            <th width="100">Status</th>
                            <th width="100">Views</th>
                            <th width="150">Created</th>
                            <th width="180">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($posts as $index => $post): ?>
                            <tr>
                                <td><?= ($index + 1) + (10 * ($pager->getCurrentPage() - 1)) ?></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <?php if (!empty($post['featured_image']) && file_exists(FCPATH . $post['featured_image'])): ?>
                                            <div class="me-2">
                                                <img src="<?= base_url($post['featured_image']) ?>" alt="<?= esc($post['title']) ?>" class="img-thumbnail" width="50">
                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <div class="fw-bold"><?= esc($post['title']) ?></div>
                                            <div class="small text-muted">Slug: <?= esc($post['slug']) ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td><?= esc($post['category_name']) ?></td>
                                <td>
                                    <?php if ($post['status'] === 'published'): ?>
                                        <span class="badge bg-success">Published</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Draft</span>
                                    <?php endif; ?>
                                </td>
                                <td><?= $post['views'] ?></td>
                                <td><?= date('M d, Y', strtotime($post['created_at'])) ?></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="<?= site_url('blog-posts/edit/' . $post['id']) ?>" class="btn btn-outline-primary">
                                            <i class="bi bi-pencil"></i> Edit
                                        </a>
                                        <a href="<?= site_url('blog-posts/comments/' . $post['id']) ?>" class="btn btn-outline-info">
                                            <i class="bi bi-chat-dots"></i> Comments
                                        </a>
                                        <a href="<?= site_url('blog-posts/delete/' . $post['id']) ?>" class="btn btn-outline-danger" onclick="return confirm('Are you sure you want to delete this post?')">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <div class="mt-4">
                <?= $pager->links('posts', 'admin_full') ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>
