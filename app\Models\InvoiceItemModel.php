<?php

namespace App\Models;

use CodeIgniter\Model;

class InvoiceItemModel extends Model
{
    protected $table            = 'invoice_items';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['invoice_id', 'description', 'fee', 'created_at', 'updated_at'];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules      = [
        'invoice_id'  => 'required|integer',
        'description' => 'required|max_length[255]',
        'fee'         => 'required|numeric',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    /**
     * Get all items for an invoice
     *
     * @param int $invoiceId
     * @return array
     */
    public function getItemsByInvoiceId($invoiceId)
    {
        return $this->where('invoice_id', $invoiceId)
                    ->orderBy('id', 'ASC')
                    ->findAll();
    }

    /**
     * Calculate invoice totals
     *
     * @param int $invoiceId
     * @return array
     */
    public function calculateInvoiceTotals($invoiceId)
    {
        $result = $this->selectSum('fee')
                       ->where('invoice_id', $invoiceId)
                       ->get()
                       ->getRowArray();

        $subtotal = $result['fee'] ?? 0;

        return [
            'subtotal' => $subtotal,
            'tax'      => 0, // You can implement tax calculation if needed
            'total'    => $subtotal
        ];
    }
}
