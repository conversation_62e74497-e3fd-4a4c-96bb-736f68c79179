<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-end mb-4">
    <a href="<?= site_url('home-slider/new') ?>" class="btn btn-primary">Add New Slider Item</a>
</div>

<?php if (session()->has('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?= session('success') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Image</th>
                        <th>Message</th>
                        <th>Paragraph</th>
                        <th>Order</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($sliders)): ?>
                        <tr>
                            <td colspan="7" class="text-center">No slider items found</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($sliders as $slider): ?>
                            <tr>
                                <td><?= $slider['id'] ?></td>
                                <td>
                                    <?php if ($slider['image']): ?>
                                        <img src="<?= base_url('public/uploads/slider/' . $slider['image']) ?>" alt="<?= $slider['message'] ?>" width="100" class="img-thumbnail">
                                    <?php else: ?>
                                        No Image
                                    <?php endif; ?>
                                </td>
                                <td><?= $slider['message'] ?></td>
                                <td><?= substr($slider['paragraph'], 0, 50) . (strlen($slider['paragraph']) > 50 ? '...' : '') ?></td>
                                <td><?= $slider['order'] ?></td>
                                <td>
                                    <span class="badge bg-<?= $slider['status'] === 'active' ? 'success' : 'danger' ?>">
                                        <?= ucfirst($slider['status']) ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="<?= site_url('home-slider/edit/' . $slider['id']) ?>" class="btn btn-primary">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="<?= site_url('home-slider/delete/' . $slider['id']) ?>" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this slider item?')">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <?php if (!empty($pager)) : ?>
            <?= $pager->links('home_slider', 'admin_full') ?>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>
