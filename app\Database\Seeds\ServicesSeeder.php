<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class ServicesSeeder extends Seeder
{
    public function run()
    {
        $data = [
            [
                'title'       => 'Web Development',
                'description' => 'We create responsive and user-friendly websites tailored to your business needs.',
                'icon'        => 'bi bi-code-slash',
                'status'      => 'active',
                'created_at'  => date('Y-m-d H:i:s'),
                'updated_at'  => date('Y-m-d H:i:s'),
            ],
            [
                'title'       => 'Mobile App Development',
                'description' => 'We build native and cross-platform mobile applications for iOS and Android.',
                'icon'        => 'bi bi-phone',
                'status'      => 'active',
                'created_at'  => date('Y-m-d H:i:s'),
                'updated_at'  => date('Y-m-d H:i:s'),
            ],
            [
                'title'       => 'UI/UX Design',
                'description' => 'We create beautiful and intuitive user interfaces that enhance user experience.',
                'icon'        => 'bi bi-palette',
                'status'      => 'active',
                'created_at'  => date('Y-m-d H:i:s'),
                'updated_at'  => date('Y-m-d H:i:s'),
            ],
            [
                'title'       => 'Digital Marketing',
                'description' => 'We help you reach your target audience through effective digital marketing strategies.',
                'icon'        => 'bi bi-graph-up',
                'status'      => 'active',
                'created_at'  => date('Y-m-d H:i:s'),
                'updated_at'  => date('Y-m-d H:i:s'),
            ],
        ];

        $this->db->table('services')->insertBatch($data);
        
        echo "Services seeded successfully.\n";
    }
}
