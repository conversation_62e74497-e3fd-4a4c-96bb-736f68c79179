<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div id="lastUpdatedInfo" class="text-muted small">
            <i class="bi bi-clock-history me-1"></i> Last updated: <?= date('M d, Y h:i A', strtotime($lastUpdated)) ?>
        </div>
        <div class="d-flex gap-2">
            <button id="refreshButton" class="btn btn-sm btn-primary" <?= ($year != $currentYear || $month != $currentMonth) ? 'disabled' : '' ?>>
                <i class="bi bi-arrow-clockwise me-1"></i> Refresh Data
            </button>
            <a href="<?= site_url('revenue-reports') ?>" class="btn btn-sm btn-outline-primary">
                <i class="bi bi-arrow-left me-1"></i> Back to Reports
            </a>
        </div>
    </div>

    <div id="alertContainer"></div>

    <!-- Monthly Overview -->
    <div class="row mb-4" id="monthlyOverview">
        <div class="col-md-6 col-xl-3 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar rounded-circle bg-success bg-opacity-10 p-3">
                                <i class="bi bi-cash-stack text-success fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-0 text-uppercase fs-xs fw-semibold">Total Revenue</h6>
                            <h3 class="mb-0 fw-bold" id="totalRevenue">₹<?= number_format($monthlyRevenue['total_revenue'] ?? 0, 2) ?></h3>
                            <p class="text-muted mb-0 small">Paid invoices only</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-xl-3 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar rounded-circle bg-success bg-opacity-10 p-3">
                                <i class="bi bi-check-circle text-success fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-0 text-uppercase fs-xs fw-semibold">Paid Invoices</h6>
                            <h3 class="mb-0 fw-bold" id="paidCount"><?= $monthlyRevenue['paid_count'] ?? 0 ?></h3>
                            <p class="text-muted mb-0 small" id="paidAmount">₹<?= number_format($monthlyRevenue['paid_amount'] ?? 0, 2) ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-xl-3 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar rounded-circle bg-info bg-opacity-10 p-3">
                                <i class="bi bi-envelope text-info fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-0 text-uppercase fs-xs fw-semibold">Sent Invoices</h6>
                            <h3 class="mb-0 fw-bold" id="sentCount"><?= $monthlyRevenue['sent_count'] ?? 0 ?></h3>
                            <p class="text-muted mb-0 small" id="sentAmount">₹<?= number_format($monthlyRevenue['sent_amount'] ?? 0, 2) ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-xl-3 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar rounded-circle bg-secondary bg-opacity-10 p-3">
                                <i class="bi bi-file-earmark text-secondary fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-0 text-uppercase fs-xs fw-semibold">Draft Invoices</h6>
                            <h3 class="mb-0 fw-bold" id="draftCount"><?= $monthlyRevenue['draft_count'] ?? 0 ?></h3>
                            <p class="text-muted mb-0 small" id="draftAmount">₹<?= number_format($monthlyRevenue['draft_amount'] ?? 0, 2) ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Daily Revenue Chart -->
    <div class="card mb-4 border-0 shadow-sm">
        <div class="card-header bg-transparent d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Daily Revenue</h5>
            <div id="chartLoadingIndicator" class="spinner-border spinner-border-sm text-primary d-none" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
        <div class="card-body">
            <div style="height: 300px;" id="chartContainer">
                <canvas id="dailyRevenueChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Daily Revenue Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-transparent d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Daily Revenue Breakdown</h5>
            <div id="tableLoadingIndicator" class="spinner-border spinner-border-sm text-primary d-none" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
        <div class="card-body" id="dailyRevenueTableContainer">
            <?php if (empty($dailyRevenue)): ?>
                <div class="text-center py-4">
                    <p class="text-muted">No revenue data available for this month.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Date</th>
                                <th>Revenue</th>
                                <th>Paid Invoices</th>
                            </tr>
                        </thead>
                        <tbody id="dailyRevenueTableBody">
                            <?php foreach ($dailyRevenue as $day): ?>
                                <tr>
                                    <td><?= date('d M Y (D)', strtotime($day['date'])) ?></td>
                                    <td>₹<?= number_format($day['paid_amount'], 2) ?></td>
                                    <td><span class="badge bg-success"><?= $day['paid_count'] ?></span></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Daily Revenue Chart
    let dailyRevenueChart;
    const dailyRevenueData = <?= json_encode($dailyRevenue) ?>;

    function initializeChart(data) {
        if (data.length > 0) {
            const ctx = document.getElementById('dailyRevenueChart').getContext('2d');

            const dates = data.map(item => {
                const date = new Date(item.date);
                return date.getDate(); // Just the day number
            });

            const revenue = data.map(item => parseFloat(item.paid_amount));

            dailyRevenueChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: dates,
                    datasets: [{
                        label: 'Daily Revenue',
                        data: revenue,
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        borderColor: 'rgba(16, 185, 129, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: 'rgba(16, 185, 129, 1)',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 4,
                        pointHoverRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '₹' + value.toLocaleString('en-IN');
                                }
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Day of Month'
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                title: function(tooltipItems) {
                                    const dayIndex = tooltipItems[0].dataIndex;
                                    const fullDate = new Date(data[dayIndex].date);
                                    return fullDate.toLocaleDateString('en-IN', {
                                        weekday: 'long',
                                        year: 'numeric',
                                        month: 'long',
                                        day: 'numeric'
                                    });
                                },
                                label: function(context) {
                                    return 'Revenue: ₹' + context.parsed.y.toLocaleString('en-IN');
                                },
                                afterLabel: function(context) {
                                    const dayIndex = context.dataIndex;
                                    return 'Invoices: ' + data[dayIndex].paid_count;
                                }
                            }
                        }
                    }
                }
            });
        }
    }

    // Initialize chart with initial data
    initializeChart(dailyRevenueData);

    // Handle refresh button click
    const refreshButton = document.getElementById('refreshButton');
    const lastUpdatedInfo = document.getElementById('lastUpdatedInfo');
    const alertContainer = document.getElementById('alertContainer');
    const chartLoadingIndicator = document.getElementById('chartLoadingIndicator');
    const tableLoadingIndicator = document.getElementById('tableLoadingIndicator');
    const dailyRevenueTableContainer = document.getElementById('dailyRevenueTableContainer');

    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            // Show loading state
            refreshButton.disabled = true;
            refreshButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Refreshing...';
            chartLoadingIndicator.classList.remove('d-none');
            tableLoadingIndicator.classList.remove('d-none');

            // Make AJAX request to refresh data
            fetch('<?= site_url("revenue-reports/monthly/{$year}/{$month}") ?>?refresh=true&ajax=true')
                .then(response => response.json())
                .then(data => {
                    // Update last updated time
                    lastUpdatedInfo.innerHTML = '<i class="bi bi-clock-history me-1"></i> Last updated: ' + data.lastUpdated;

                    // Show success message
                    alertContainer.innerHTML = '<div class="alert alert-success alert-dismissible fade show">' +
                        'Revenue data refreshed successfully.' +
                        '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                        '</div>';

                    // Update monthly overview cards
                    document.getElementById('totalRevenue').textContent = '₹' + parseFloat(data.monthlyRevenue.total_revenue).toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                    document.getElementById('paidCount').textContent = data.monthlyRevenue.paid_count;
                    document.getElementById('paidAmount').textContent = '₹' + parseFloat(data.monthlyRevenue.paid_amount).toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                    document.getElementById('sentCount').textContent = data.monthlyRevenue.sent_count;
                    document.getElementById('sentAmount').textContent = '₹' + parseFloat(data.monthlyRevenue.sent_amount).toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                    document.getElementById('draftCount').textContent = data.monthlyRevenue.draft_count;
                    document.getElementById('draftAmount').textContent = '₹' + parseFloat(data.monthlyRevenue.draft_amount).toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2});

                    // Update chart data
                    if (dailyRevenueChart) {
                        dailyRevenueChart.destroy();
                    }
                    initializeChart(data.dailyRevenue);

                    // Update table data
                    if (data.dailyTableHtml) {
                        dailyRevenueTableContainer.innerHTML = data.dailyTableHtml;
                    }

                    // Reset loading states
                    refreshButton.disabled = false;
                    refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i> Refresh Data';
                    chartLoadingIndicator.classList.add('d-none');
                    tableLoadingIndicator.classList.add('d-none');

                    // Auto-dismiss alert after 5 seconds
                    setTimeout(() => {
                        const alert = document.querySelector('.alert');
                        if (alert) {
                            const bsAlert = new bootstrap.Alert(alert);
                            bsAlert.close();
                        }
                    }, 5000);
                })
                .catch(error => {
                    console.error('Error refreshing data:', error);

                    // Show error message
                    alertContainer.innerHTML = '<div class="alert alert-danger alert-dismissible fade show">' +
                        'Failed to refresh revenue data. Please try again.' +
                        '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                        '</div>';

                    // Reset loading states
                    refreshButton.disabled = false;
                    refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i> Refresh Data';
                    chartLoadingIndicator.classList.add('d-none');
                    tableLoadingIndicator.classList.add('d-none');
                });
        });
    }
});
</script>

<?= $this->endSection() ?>
