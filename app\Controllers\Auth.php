<?php

namespace App\Controllers;

use App\Models\UserModel;

class Auth extends BaseController
{
    public function login()
    {
        if (session()->get('isLoggedIn')) {
            return redirect()->to('/admin');
        }

        return view('auth/login');
    }

    public function attemptLogin()
    {
        $rules = [
            'email'    => 'required|valid_email',
            'password' => 'required|min_length[6]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $userModel = new UserModel();
        $user = $userModel->where('email', $this->request->getPost('email'))->first();

        if (!$user || !password_verify($this->request->getPost('password'), $user['password'])) {
            return redirect()->back()->withInput()->with('error', 'Invalid login credentials');
        }

        $this->setUserSession($user);
        return redirect()->to('/admin');
    }

    private function setUserSession($user)
    {
        $data = [
            'id'         => $user['id'],
            'name'       => $user['name'],
            'email'      => $user['email'],
            'role'       => $user['role'] ?? 'admin',
            'isLoggedIn' => true,
        ];

        session()->set($data);

        // Update last login time
        $userModel = new UserModel();
        $userModel->update($user['id'], ['last_login' => date('Y-m-d H:i:s')]);

        // Create notification for login
        $notificationModel = new \App\Models\NotificationModel();
        $notificationModel->createNotification(
            $user['id'],
            'login',
            'New Login',
            'You have logged in successfully at ' . date('Y-m-d H:i:s'),
            null,
            'bi-box-arrow-in-right'
        );
    }

    public function logout()
    {
        session()->destroy();
        return redirect()->to('/auth/login');
    }

    // Forgot Password
    public function forgotPassword()
    {
        return view('auth/forgot_password');
    }

    // Process Forgot Password
    public function processForgotPassword()
    {
        $rules = [
            'email' => 'required|valid_email',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $userModel = new UserModel();
        $user = $userModel->where('email', $this->request->getPost('email'))->first();

        if (!$user) {
            return redirect()->back()->withInput()->with('error', 'Email not found in our records');
        }

        // Generate reset token
        $token = bin2hex(random_bytes(16));
        $expiry = date('Y-m-d H:i:s', strtotime('+1 hour'));

        // Store token in session (in a real app, you'd store this in the database)
        session()->set('reset_token', [
            'email' => $user['email'],
            'token' => $token,
            'expiry' => $expiry
        ]);

        // In a real application, you would send an email with the reset link
        // For this demo, we'll just redirect to the reset password page with the token

        return redirect()->to('/auth/resetPassword/' . $token)->with('message', 'Password reset link has been generated. In a real application, this would be sent to your email.');
    }

    // Reset Password Form
    public function resetPassword($token = null)
    {
        if (!$token) {
            return redirect()->to('/auth/login')->with('error', 'Invalid reset token');
        }

        $resetToken = session()->get('reset_token');

        if (!$resetToken || $resetToken['token'] !== $token || strtotime($resetToken['expiry']) < time()) {
            return redirect()->to('/auth/login')->with('error', 'Invalid or expired reset token');
        }

        $data = [
            'token' => $token,
            'email' => $resetToken['email']
        ];

        return view('auth/reset_password', $data);
    }

    // Process Reset Password
    public function processResetPassword()
    {
        $rules = [
            'token' => 'required',
            'email' => 'required|valid_email',
            'password' => 'required|min_length[6]',
            'confirm_password' => 'required|matches[password]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $token = $this->request->getPost('token');
        $email = $this->request->getPost('email');
        $password = $this->request->getPost('password');

        $resetToken = session()->get('reset_token');

        if (!$resetToken || $resetToken['token'] !== $token || $resetToken['email'] !== $email || strtotime($resetToken['expiry']) < time()) {
            return redirect()->to('/auth/login')->with('error', 'Invalid or expired reset token');
        }

        // Update user password
        $userModel = new UserModel();
        $user = $userModel->where('email', $email)->first();

        if (!$user) {
            return redirect()->to('/auth/login')->with('error', 'User not found');
        }

        $userModel->update($user['id'], ['password' => $password]);

        // Clear reset token
        session()->remove('reset_token');

        return redirect()->to('/auth/login')->with('message', 'Password has been reset successfully. You can now login with your new password.');
    }

    // For first time setup - create an admin user
    public function setup()
    {
        // Check if any user exists
        $userModel = new UserModel();
        $existingUsers = $userModel->countAllResults();

        if ($existingUsers > 0) {
            return redirect()->to('/auth/login')->with('error', 'Setup has already been completed');
        }

        // Create admin user
        $data = [
            'name'     => 'Admin',
            'email'    => '<EMAIL>',
            'password' => 'password',
        ];

        $userModel->insert($data);
        return redirect()->to('/auth/login')->with('message', 'Admin user created. You can now login with email: <EMAIL> and password: password');
    }
}
