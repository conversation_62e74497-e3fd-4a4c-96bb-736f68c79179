<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-end align-items-center mb-4">
    <a href="<?= site_url('blog-posts') ?>" class="btn btn-secondary">
        <i class="bi bi-arrow-left me-1"></i> Back to Posts
    </a>
</div>

<?= $this->include('admin/partials/alerts') ?>

<div class="card mb-4">
    <div class="card-body">
        <div class="d-flex align-items-center">
            <?php if (!empty($post['featured_image']) && file_exists(FCPATH . $post['featured_image'])): ?>
                <div class="me-3">
                    <img src="<?= base_url($post['featured_image']) ?>" alt="<?= esc($post['title']) ?>" class="img-thumbnail" width="100">
                </div>
            <?php endif; ?>
            <div>
                <h5 class="card-title mb-1"><?= esc($post['title']) ?></h5>
                <p class="card-text text-muted mb-0">
                    <small>
                        <i class="bi bi-calendar me-1"></i> <?= date('F j, Y', strtotime($post['created_at'])) ?>
                        <span class="mx-2">|</span>
                        <i class="bi bi-eye me-1"></i> <?= $post['views'] ?> views
                    </small>
                </p>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <?php if (empty($comments)): ?>
            <div class="text-center py-5">
                <div class="mb-3">
                    <i class="bi bi-chat-square-text fs-1 text-muted"></i>
                </div>
                <h5>No Comments Found</h5>
                <p class="text-muted">This post has no comments yet.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead>
                        <tr>
                            <th width="50">#</th>
                            <th>Author</th>
                            <th>Comment</th>
                            <th width="120">Status</th>
                            <th width="150">Date</th>
                            <th width="150">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($comments as $index => $comment): ?>
                            <tr>
                                <td><?= ($index + 1) + (10 * ($pager->getCurrentPage() - 1)) ?></td>
                                <td>
                                    <div class="fw-bold"><?= esc($comment['name']) ?></div>
                                    <div class="small text-muted"><?= esc($comment['email']) ?></div>
                                    <?php if (!empty($comment['website'])): ?>
                                        <div class="small">
                                            <a href="<?= esc($comment['website']) ?>" target="_blank"><?= esc($comment['website']) ?></a>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td><?= esc($comment['comment']) ?></td>
                                <td>
                                    <form action="<?= site_url('blog-posts/update-comment-status/' . $comment['id']) ?>" method="post">
                                        <?= csrf_field() ?>
                                        <select class="form-select form-select-sm status-select" name="status" data-comment-id="<?= $comment['id'] ?>">
                                            <option value="pending" <?= $comment['status'] === 'pending' ? 'selected' : '' ?>>Pending</option>
                                            <option value="approved" <?= $comment['status'] === 'approved' ? 'selected' : '' ?>>Approved</option>
                                            <option value="spam" <?= $comment['status'] === 'spam' ? 'selected' : '' ?>>Spam</option>
                                        </select>
                                    </form>
                                </td>
                                <td><?= date('M d, Y g:i a', strtotime($comment['created_at'])) ?></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="<?= site_url('blog-posts/delete-comment/' . $comment['id']) ?>" class="btn btn-outline-danger" onclick="return confirm('Are you sure you want to delete this comment?')">
                                            <i class="bi bi-trash"></i> Delete
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <div class="mt-4">
                <?= $pager->links('comments', 'admin_full') ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit status change
    const statusSelects = document.querySelectorAll('.status-select');

    statusSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.closest('form').submit();
        });
    });
});
</script>
<?= $this->endSection() ?>
