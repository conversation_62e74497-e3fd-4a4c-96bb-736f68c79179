<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\BlogPostModel;
use App\Models\BlogCategoryModel;
use App\Models\BlogTagModel;
use App\Models\BlogCommentModel;

class BlogPost extends BaseController
{
    protected $blogPostModel;
    protected $blogCategoryModel;
    protected $blogTagModel;
    protected $blogCommentModel;

    public function __construct()
    {
        $this->blogPostModel = new BlogPostModel();
        $this->blogCategoryModel = new BlogCategoryModel();
        $this->blogTagModel = new BlogTagModel();
        $this->blogCommentModel = new BlogCommentModel();
    }

    public function index()
    {
        $data = [
            'title' => 'Manage Blog Posts',
            'posts' => $this->blogPostModel->select('blog_posts.*, blog_categories.name as category_name')
                ->join('blog_categories', 'blog_categories.id = blog_posts.category_id')
                ->orderBy('blog_posts.created_at', 'DESC')
                ->paginate(10, 'posts'),
            'pager' => $this->blogPostModel->pager,
        ];

        return view('admin/blog/posts/index', $data);
    }

    public function new()
    {
        $data = [
            'title'      => 'Add New Post',
            'categories' => $this->blogCategoryModel->orderBy('name', 'ASC')->findAll(),
            'tags'       => $this->blogTagModel->orderBy('name', 'ASC')->findAll(),
        ];

        return view('admin/blog/posts/new', $data);
    }

    public function create()
    {
        $rules = [
            'title'       => 'required|min_length[3]|max_length[255]',
            'slug'        => 'required|min_length[3]|max_length[255]|is_unique[blog_posts.slug]',
            'content'     => 'required',
            'category_id' => 'required|integer',
            'status'      => 'required|in_list[draft,published]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Handle featured image upload
        $featuredImage = $this->request->getFile('featured_image');
        $featuredImagePath = null;

        if ($featuredImage && $featuredImage->isValid() && !$featuredImage->hasMoved()) {
            $newName = $featuredImage->getRandomName();
            $featuredImage->move(FCPATH . 'uploads/blog', $newName);
            $featuredImagePath = 'uploads/blog/' . $newName;
        }

        // Prepare post data
        $data = [
            'title'            => $this->request->getPost('title'),
            'slug'             => $this->request->getPost('slug'),
            'content'          => $this->request->getPost('content'),
            'excerpt'          => $this->request->getPost('excerpt'),
            'featured_image'   => $featuredImagePath,
            'category_id'      => $this->request->getPost('category_id'),
            'status'           => $this->request->getPost('status'),
            'author_id'        => session()->get('id'), // Changed from user_id to id
            'meta_title'       => $this->request->getPost('meta_title'),
            'meta_description' => $this->request->getPost('meta_description'),
            'meta_keywords'    => $this->request->getPost('meta_keywords'),
        ];

        // Insert post
        $postId = $this->blogPostModel->insert($data);

        if ($postId) {
            // Save tags
            $tags = $this->request->getPost('tags') ?? [];
            $this->blogTagModel->savePostTags($postId, $tags);

            // Create notification for the current user
            $userId = session()->get('id');
            if ($userId) {
                create_notification(
                    $userId,
                    'blog',
                    'New Blog Post Created',
                    'Blog post "' . $data['title'] . '" has been created',
                    site_url('blog-posts/edit/' . $postId),
                    'bi-newspaper'
                );
            }

            return redirect()->to('blog-posts')->with('success', 'Post added successfully.');
        }

        return redirect()->back()->withInput()->with('error', 'Failed to add post. Please try again.');
    }

    public function edit($id = null)
    {
        $post = $this->blogPostModel->find($id);

        if (!$post) {
            return redirect()->to('blog-posts')->with('error', 'Post not found.');
        }

        // Get post tags
        $postTags = $this->blogTagModel->getTagsByPostId($id);
        $selectedTags = array_column($postTags, 'id');

        $data = [
            'title'        => 'Edit Post',
            'post'         => $post,
            'categories'   => $this->blogCategoryModel->orderBy('name', 'ASC')->findAll(),
            'tags'         => $this->blogTagModel->orderBy('name', 'ASC')->findAll(),
            'selectedTags' => $selectedTags,
        ];

        return view('admin/blog/posts/edit', $data);
    }

    public function update($id = null)
    {
        $post = $this->blogPostModel->find($id);

        if (!$post) {
            return redirect()->to('blog-posts')->with('error', 'Post not found.');
        }

        $rules = [
            'title'       => 'required|min_length[3]|max_length[255]',
            'slug'        => "required|min_length[3]|max_length[255]|is_unique[blog_posts.slug,id,$id]",
            'content'     => 'required',
            'category_id' => 'required|integer',
            'status'      => 'required|in_list[draft,published]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Handle featured image upload
        $featuredImage = $this->request->getFile('featured_image');
        $featuredImagePath = $post['featured_image'];

        // Log file upload information for debugging
        log_message('debug', 'Featured image upload (update): ' . print_r($featuredImage, true));

        if ($featuredImage && $featuredImage->isValid() && !$featuredImage->hasMoved()) {
            try {
                // Delete old image if exists
                if ($featuredImagePath && file_exists(FCPATH . $featuredImagePath)) {
                    unlink(FCPATH . $featuredImagePath);
                    log_message('debug', 'Deleted old featured image: ' . $featuredImagePath);
                }

                $newName = $featuredImage->getRandomName();

                // Make sure the directory exists
                if (!is_dir(FCPATH . 'uploads/blog')) {
                    mkdir(FCPATH . 'uploads/blog', 0777, true);
                }

                // Move the file
                $featuredImage->move(FCPATH . 'uploads/blog', $newName);
                $featuredImagePath = 'uploads/blog/' . $newName;

                // Log success
                log_message('debug', 'Featured image updated to: ' . $featuredImagePath);
            } catch (\Exception $e) {
                // Log error
                log_message('error', 'Failed to update featured image: ' . $e->getMessage());
            }
        } else {
            // Log why the file wasn't processed
            if ($featuredImage->getError() !== UPLOAD_ERR_NO_FILE) {
                log_message('debug', 'Featured image is not valid: ' . print_r($featuredImage->getError(), true));
            }
        }

        // Prepare post data
        $data = [
            'title'            => $this->request->getPost('title'),
            'slug'             => $this->request->getPost('slug'),
            'content'          => $this->request->getPost('content'),
            'excerpt'          => $this->request->getPost('excerpt'),
            'featured_image'   => $featuredImagePath,
            'category_id'      => $this->request->getPost('category_id'),
            'status'           => $this->request->getPost('status'),
            'author_id'        => $post['author_id'], // Keep the original author_id
            'meta_title'       => $this->request->getPost('meta_title'),
            'meta_description' => $this->request->getPost('meta_description'),
            'meta_keywords'    => $this->request->getPost('meta_keywords'),
            'updated_at'       => date('Y-m-d H:i:s'), // Set updated_at manually
        ];

        // Update published_at if status changes to published
        if ($data['status'] === 'published' && ($post['status'] !== 'published' || empty($post['published_at']))) {
            $data['published_at'] = date('Y-m-d H:i:s');
        }

        try {
            // Use direct database query to bypass model validation
            $db = \Config\Database::connect();
            $builder = $db->table('blog_posts');
            $builder->where('id', $id);
            $result = $builder->update($data);

            if ($result) {
                // Save tags
                $tags = $this->request->getPost('tags') ?? [];
                $this->blogTagModel->savePostTags($id, $tags);

                return redirect()->to('blog-posts')->with('success', 'Post updated successfully.');
            } else {
                // Log the error
                log_message('error', 'Failed to update post: ' . print_r($db->error(), true));
                return redirect()->back()->withInput()->with('error', 'Failed to update post. Database error.');
            }
        } catch (\Exception $e) {
            // Log the exception
            log_message('error', 'Exception when updating post: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Failed to update post: ' . $e->getMessage());
        }
    }

    public function delete($id = null)
    {
        $post = $this->blogPostModel->find($id);

        if (!$post) {
            return redirect()->to('blog-posts')->with('error', 'Post not found.');
        }

        // Delete featured image if exists
        if ($post['featured_image'] && file_exists(FCPATH . $post['featured_image'])) {
            unlink(FCPATH . $post['featured_image']);
        }

        if ($this->blogPostModel->delete($id)) {
            return redirect()->to('blog-posts')->with('success', 'Post deleted successfully.');
        }

        return redirect()->to('blog-posts')->with('error', 'Failed to delete post. Please try again.');
    }

    /**
     * Generate slug from title
     */
    public function generateSlug()
    {
        $title = $this->request->getPost('title');
        $slug = url_title($title, '-', true);

        return $this->response->setJSON(['slug' => $slug]);
    }

    /**
     * Manage comments for a post
     */
    public function comments($postId = null)
    {
        $post = $this->blogPostModel->find($postId);

        if (!$post) {
            return redirect()->to('blog-posts')->with('error', 'Post not found.');
        }

        $data = [
            'title'    => 'Manage Comments for: ' . $post['title'],
            'post'     => $post,
            'comments' => $this->blogCommentModel->where('post_id', $postId)
                ->orderBy('created_at', 'DESC')
                ->paginate(10, 'comments'),
            'pager'    => $this->blogCommentModel->pager,
        ];

        return view('admin/blog/comments/index', $data);
    }

    /**
     * Update comment status
     */
    public function updateCommentStatus($commentId = null)
    {
        $comment = $this->blogCommentModel->find($commentId);

        if (!$comment) {
            return redirect()->back()->with('error', 'Comment not found.');
        }

        $status = $this->request->getPost('status');

        if (!in_array($status, ['pending', 'approved', 'spam'])) {
            return redirect()->back()->with('error', 'Invalid status.');
        }

        if ($this->blogCommentModel->update($commentId, ['status' => $status])) {
            return redirect()->back()->with('success', 'Comment status updated successfully.');
        }

        return redirect()->back()->with('error', 'Failed to update comment status. Please try again.');
    }

    /**
     * Delete comment
     */
    public function deleteComment($commentId = null)
    {
        $comment = $this->blogCommentModel->find($commentId);

        if (!$comment) {
            return redirect()->back()->with('error', 'Comment not found.');
        }

        if ($this->blogCommentModel->delete($commentId)) {
            return redirect()->back()->with('success', 'Comment deleted successfully.');
        }

        return redirect()->back()->with('error', 'Failed to delete comment. Please try again.');
    }
}
