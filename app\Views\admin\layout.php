<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Admin Dashboard' ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/simplebar@5.3.8/dist/simplebar.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --primary-light: #a5b4fc;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --info-color: #06b6d4;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --light-color: #f8fafc;
            --dark-color: #1e293b;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
            --sidebar-width: 250px;
            --topbar-height: 60px;
        }

        body {
            font-family: 'Poppins', sans-serif;
            font-size: 0.9rem;
            background-color: var(--gray-100);
            color: var(--gray-700);
            overflow-x: hidden;
            width: 100%;
            max-width: 100%;
            position: relative;
        }

        .feather {
            width: 18px;
            height: 18px;
            stroke-width: 2;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            width: var(--sidebar-width);
            z-index: 1030;
            padding: var(--topbar-height) 0 0;
            background-color: #fff;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: all 0.3s ease;
            overflow-y: auto;
            overflow-x: hidden;
        }

        @media (max-width: 991.98px) {
            .sidebar {
                transform: translateX(-100%);
                width: 240px;
            }

            .sidebar.show {
                transform: translateX(0);
            }
        }

        .sidebar-sticky {
            height: calc(100vh - var(--topbar-height));
            padding: 1rem;
            overflow-y: auto;
        }

        .sidebar .nav {
            margin-bottom: 1.5rem;
        }

        .sidebar .nav-item {
            margin-bottom: 0.25rem;
        }

        .sidebar .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: var(--gray-700);
            border-radius: 0.5rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .sidebar .nav-link:hover {
            color: var(--primary-color);
            background-color: var(--gray-100);
        }

        .sidebar .nav-link.active {
            color: var(--primary-color);
            background-color: rgba(99, 102, 241, 0.1);
        }

        .sidebar .nav-link i,
        .sidebar .nav-link svg {
            margin-right: 0.75rem;
            color: var(--gray-500);
            transition: all 0.2s ease;
        }

        .sidebar .nav-link:hover i,
        .sidebar .nav-link:hover svg,
        .sidebar .nav-link.active i,
        .sidebar .nav-link.active svg {
            color: var(--primary-color);
        }

        .sidebar-heading {
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: var(--gray-500);
            font-weight: 600;
            padding: 0.5rem 1rem;
            margin-bottom: 0.5rem;
        }

        /* Navbar Styles */
        .navbar {
            height: var(--topbar-height);
            padding: 0 1rem;
            background-color: #fff;
            border-bottom: 1px solid var(--gray-200);
            z-index: 1040;
            width: 100%;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.25rem;
            color: var(--gray-800);
            padding: 0;
        }

        .navbar-brand svg,
        .navbar-brand img {
            height: 32px;
            margin-right: 0.5rem;
        }

        .navbar .navbar-toggler {
            border: none;
            padding: 0;
            font-size: 1.5rem;
            color: var(--gray-700);
        }

        .navbar .navbar-toggler:focus {
            box-shadow: none;
        }

        .navbar-nav .nav-item {
            margin-left: 0.5rem;
        }

        .navbar-nav .nav-link {
            padding: 0.5rem 0.75rem;
            color: var(--gray-700);
            border-radius: 0.375rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .navbar-nav .nav-link:hover {
            color: var(--primary-color);
            background-color: var(--gray-100);
        }

        .dropdown-menu {
            padding: 0.5rem;
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
            border-radius: 0.5rem;
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            color: var(--gray-700);
            font-weight: 500;
            border-radius: 0.375rem;
            transition: all 0.2s ease;
        }

        .dropdown-item:hover,
        .dropdown-item:focus {
            color: var(--primary-color);
            background-color: var(--gray-100);
        }

        /* Main Content Styles */
        .main-content {
            margin-left: var(--sidebar-width);
            padding: calc(var(--topbar-height) + 1rem) 1rem 1rem;
            min-height: 100vh;
            transition: all 0.3s ease;
            width: calc(100% - var(--sidebar-width));
            max-width: 100%;
            overflow-x: hidden;
        }

        @media (max-width: 991.98px) {
            .main-content {
                margin-left: 0;
                width: 100%;
                padding: calc(var(--topbar-height) + 1rem) 0.75rem 1rem;
            }
        }

        @media (min-width: 992px) and (max-width: 1199.98px) {
            .main-content {
                padding: calc(var(--topbar-height) + 1rem) 0.75rem 1rem;
            }
        }

        .page-title {
            font-weight: 500;
            color: var(--gray-800);
        }

        /* Card Styles */
        .card {
            border: none;
            border-radius: 0.75rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 1.25rem;
            transition: all 0.2s ease;
            overflow: hidden;
            width: 100%;
        }

        .card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        }

        .card-header {
            background-color: transparent;
            border-bottom: 1px solid var(--gray-200);
            padding: 1rem 1.25rem;
            font-weight: 600;
            color: var(--gray-800);
        }

        .card-body {
            padding: 1.25rem;
        }

        @media (max-width: 767.98px) {
            .card-header {
                padding: 0.75rem 1rem;
            }

            .card-body {
                padding: 1rem;
            }
        }

        /* Alert Styles */
        .alert {
            border: none;
            border-radius: 0.5rem;
            padding: 1rem 1.25rem;
            margin-bottom: 1.5rem;
        }

        .alert-success {
            background-color: rgba(16, 185, 129, 0.1);
            color: #047857;
        }

        .alert-danger {
            background-color: rgba(239, 68, 68, 0.1);
            color: #b91c1c;
        }

        /* Button Styles */
        .btn {
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: 0 4px 6px rgba(99, 102, 241, 0.2);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }

        .btn-success:hover {
            background-color: #059669;
            border-color: #059669;
        }

        .btn-danger {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
        }

        .btn-danger:hover {
            background-color: #dc2626;
            border-color: #dc2626;
        }

        /* Form Styles */
        .form-control {
            padding: 0.5rem 0.75rem;
            border: 1px solid var(--gray-300);
            border-radius: 0.5rem;
            font-size: 0.9rem;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(99, 102, 241, 0.25);
        }

        .form-label {
            font-weight: 500;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
        }

        /* Table Styles */
        .table {
            color: var(--gray-700);
        }

        .table th {
            font-weight: 600;
            color: var(--gray-800);
            background-color: var(--gray-100);
            border-bottom-width: 1px;
        }

        .table td {
            vertical-align: middle;
        }

        /* Utilities */
        .text-primary {
            color: var(--primary-color) !important;
        }

        .bg-primary {
            background-color: var(--primary-color) !important;
        }

        .border-primary {
            border-color: var(--primary-color) !important;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container-fluid px-2 px-lg-3">
            <!-- Brand Logo -->
            <a class="navbar-brand" href="<?= site_url('admin') ?>">
                <svg width="28" height="28" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 0C8.954 0 0 8.954 0 20C0 31.046 8.954 40 20 40C31.046 40 40 31.046 40 20C40 8.954 31.046 0 20 0ZM20 30C14.477 30 10 25.523 10 20C10 14.477 14.477 10 20 10C25.523 10 30 14.477 30 20C30 25.523 25.523 30 20 30Z" fill="#6366F1"/>
                </svg>
                <span class="fs-6">Admin Panel</span>
            </a>

            <!-- Mobile Toggle Button -->
            <button class="navbar-toggler border-0 shadow-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
                <i class="bi bi-list"></i>
            </button>

            <!-- Right Side Nav Items -->
            <div class="ms-auto d-flex align-items-center">

                <!-- User Dropdown -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center p-1" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <?php
                        $user = session()->get();
                        $profileImage = null;

                        // Check if user has a profile image
                        if (isset($user['id'])) {
                            $userModel = new \App\Models\UserModel();
                            $userData = $userModel->find($user['id']);

                            // Debug information
                            log_message('debug', 'User data: ' . json_encode($userData));

                            if ($userData && !empty($userData['profile_image'])) {
                                $imagePath = ROOTPATH . 'public/uploads/profile/' . $userData['profile_image'];
                                log_message('debug', 'Image path: ' . $imagePath . ', Exists: ' . (file_exists($imagePath) ? 'Yes' : 'No'));

                                if (file_exists($imagePath)) {
                                    $profileImage = base_url('public/uploads/profile/' . $userData['profile_image']);
                                    log_message('debug', 'Profile image URL: ' . $profileImage);
                                }
                            }
                        }
                        ?>

                        <?php if ($profileImage): ?>
                            <img src="<?= $profileImage ?>?v=<?= time() ?>" alt="<?= session()->get('name') ?>" class="avatar avatar-sm me-2 rounded-circle" style="width: 32px; height: 32px; object-fit: cover;">
                        <?php else: ?>
                            <div class="avatar avatar-sm me-2 bg-primary text-white rounded-circle" style="width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; font-weight: 600;">
                                <span><?= substr(session()->get('name'), 0, 1) ?></span>
                            </div>
                        <?php endif; ?>

                        <span class="d-none d-md-inline small"><?= session()->get('name') ?></span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end shadow-sm">
                        <li><a class="dropdown-item small py-2" href="<?= site_url('profile') ?>"><i class="bi bi-person me-2"></i>My Profile</a></li>
                        <li><hr class="dropdown-divider my-1"></li>
                        <li><a class="dropdown-item small py-2" href="<?= site_url('auth/logout') ?>"><i class="bi bi-box-arrow-right me-2"></i>Sign Out</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Sidebar & Main Content -->
    <div class="container-fluid px-0">
        <div class="row g-0">
            <!-- Sidebar -->
            <aside id="sidebarMenu" class="sidebar collapse d-lg-block">
                <div class="sidebar-sticky" data-simplebar>
                    <!-- Brand Logo (Mobile Only) -->
                    <div class="d-flex align-items-center mb-3 d-lg-none">
                        <svg width="28" height="28" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 0C8.954 0 0 8.954 0 20C0 31.046 8.954 40 20 40C31.046 40 40 31.046 40 20C40 8.954 31.046 0 20 0ZM20 30C14.477 30 10 25.523 10 20C10 14.477 14.477 10 20 10C25.523 10 30 14.477 30 20C30 25.523 25.523 30 20 30Z" fill="#6366F1"/>
                        </svg>
                        <span class="ms-2 fw-semibold fs-6">Admin Panel</span>
                    </div>

                    <!-- Close Button (Mobile Only) -->
                    <button type="button" class="btn-close position-absolute top-2 end-2 d-lg-none" aria-label="Close" onclick="document.querySelector('.sidebar').classList.remove('show')"></button>

                    <!-- Main Navigation -->
                    <div class="sidebar-heading small">Main</div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?= uri_string() == 'admin' ? 'active' : '' ?>" href="<?= site_url('admin') ?>">
                                <i class="bi bi-speedometer2"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                    </ul>

                     <!-- Communication -->
                     <div class="sidebar-heading mt-3 small">Communication</div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?= uri_string() == 'contact' ? 'active' : '' ?>" href="<?= site_url('contact') ?>">
                                <i class="bi bi-envelope"></i>
                                <span>Messages</span>
                                <span class="badge rounded-pill bg-danger ms-auto" style="font-size: 0.65rem; padding: 0.2em 0.45em;">New</span>
                            </a>
                        </li>
                    </ul>

                    <!-- Billing -->
                    <div class="sidebar-heading mt-3 small">Billing</div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?= strpos(uri_string(), 'clients') !== false ? 'active' : '' ?>" href="<?= site_url('clients') ?>">
                                <i class="bi bi-people"></i>
                                <span>Clients</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= strpos(uri_string(), 'invoices') !== false ? 'active' : '' ?>" href="<?= site_url('invoices') ?>">
                                <i class="bi bi-receipt"></i>
                                <span>Invoices</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= strpos(uri_string(), 'revenue-reports') !== false ? 'active' : '' ?>" href="<?= site_url('revenue-reports') ?>">
                                <i class="bi bi-graph-up"></i>
                                <span>Revenue Reports</span>
                            </a>
                        </li>
                    </ul>

                    <!-- Content Management -->
                    <div class="sidebar-heading mt-3 small">Content Management</div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?= strpos(uri_string(), 'gallery') !== false ? 'active' : '' ?>" href="<?= site_url('gallery') ?>">
                                <i class="bi bi-images"></i>
                                <span>Gallery</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= strpos(uri_string(), 'home-slider') !== false ? 'active' : '' ?>" href="<?= site_url('home-slider') ?>">
                                <i class="bi bi-sliders"></i>
                                <span>Home Slider</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= strpos(uri_string(), 'services') !== false ? 'active' : '' ?>" href="<?= site_url('services') ?>">
                                <i class="bi bi-gear"></i>
                                <span>Services</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= strpos(uri_string(), 'team') !== false ? 'active' : '' ?>" href="<?= site_url('team') ?>">
                                <i class="bi bi-people"></i>
                                <span>Team</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= strpos(uri_string(), 'about') !== false && strpos(uri_string(), 'about-') === false ? 'active' : '' ?>" href="<?= site_url('about') ?>">
                                <i class="bi bi-info-circle"></i>
                                <span>About</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= strpos(uri_string(), 'about-journey') !== false ? 'active' : '' ?>" href="<?= site_url('about-journey') ?>">
                                <i class="bi bi-clock-history"></i>
                                <span>About Journey</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= strpos(uri_string(), 'about-images') !== false ? 'active' : '' ?>" href="<?= site_url('about-images') ?>">
                                <i class="bi bi-image"></i>
                                <span>About Images</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= strpos(uri_string(), 'contact-info') !== false ? 'active' : '' ?>" href="<?= site_url('contact-info') ?>">
                                <i class="bi bi-info-circle"></i>
                                <span>Contact Information</span>
                            </a>
                        </li>
                    </ul>

                    <!-- Blog Management -->
                    <div class="sidebar-heading mt-3 small">Blog Management</div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?= strpos(uri_string(), 'blog-posts') !== false ? 'active' : '' ?>" href="<?= site_url('blog-posts') ?>">
                                <i class="bi bi-file-earmark-text"></i>
                                <span>Posts</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= strpos(uri_string(), 'blog-categories') !== false ? 'active' : '' ?>" href="<?= site_url('blog-categories') ?>">
                                <i class="bi bi-folder"></i>
                                <span>Categories</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= strpos(uri_string(), 'blog-tags') !== false ? 'active' : '' ?>" href="<?= site_url('blog-tags') ?>">
                                <i class="bi bi-tags"></i>
                                <span>Tags</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= strpos(uri_string(), 'blog-comments') !== false ? 'active' : '' ?>" href="<?= site_url('blog-comments') ?>">
                                <i class="bi bi-chat-dots"></i>
                                <span>Comments</span>
                            </a>
                        </li>
                    </ul>

                </div>
            </aside>

            <!-- Main Content -->
            <main class="main-content">

                <!-- Page Title (hidden on dashboard) -->
                <?php if (uri_string() != 'admin'): ?>
                <div class="card mb-3">
                    <div class="card-body bg-primary bg-opacity-10 p-3 p-md-4">
                        <h1 class="page-title mb-0"><?= $title ?? 'Admin Dashboard' ?></h1>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Alerts -->
                <?php if (session()->has('message')): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            <div><?= session('message') ?></div>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (session()->has('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <div><?= session('error') ?></div>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Main Content -->
                <?= $this->renderSection('content') ?>
            </main>
        </div>
    </div>

    <!-- Core JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/dist/feather.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/simplebar@5.3.8/dist/simplebar.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>

    <!-- Custom JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Feather Icons
            feather.replace();

            // Initialize tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Mobile sidebar toggle
            const sidebarToggle = document.querySelector('.navbar-toggler');
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    document.querySelector('.sidebar').classList.toggle('show');
                });
            }

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(event) {
                const sidebar = document.querySelector('.sidebar');
                const sidebarToggle = document.querySelector('.navbar-toggler');

                if (window.innerWidth < 992 &&
                    sidebar &&
                    sidebar.classList.contains('show') &&
                    !sidebar.contains(event.target) &&
                    !sidebarToggle.contains(event.target)) {
                    sidebar.classList.remove('show');
                }
            });

            // Add active class to nav items based on URL
            const currentLocation = location.href;
            const menuItems = document.querySelectorAll('.sidebar .nav-link');
            const menuLength = menuItems.length;

            for (let i = 0; i < menuLength; i++) {
                if (menuItems[i].href === currentLocation) {
                    menuItems[i].classList.add('active');
                }
            }

            // Add avatar placeholder styles
            const avatars = document.querySelectorAll('.avatar');
            avatars.forEach(avatar => {
                if (avatar.classList.contains('avatar-sm')) {
                    avatar.style.width = '32px';
                    avatar.style.height = '32px';
                    avatar.style.fontSize = '0.875rem';
                } else if (avatar.classList.contains('avatar-lg')) {
                    avatar.style.width = '64px';
                    avatar.style.height = '64px';
                    avatar.style.fontSize = '1.5rem';
                } else {
                    avatar.style.width = '40px';
                    avatar.style.height = '40px';
                    avatar.style.fontSize = '1rem';
                }

                avatar.style.display = 'flex';
                avatar.style.alignItems = 'center';
                avatar.style.justifyContent = 'center';
                avatar.style.fontWeight = '600';
            });




        });
    </script>
</body>
</html>
