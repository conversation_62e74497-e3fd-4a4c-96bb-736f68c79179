<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="row">
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-body text-center">
                <?php if (!empty($user['profile_image']) && file_exists(ROOTPATH . 'public/uploads/profile/' . $user['profile_image'])): ?>
                    <img src="<?= base_url('public/uploads/profile/' . $user['profile_image']) ?>" alt="<?= $user['name'] ?>" class="rounded-circle img-thumbnail mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                <?php else: ?>
                    <div class="avatar avatar-lg bg-primary text-white rounded-circle mx-auto mb-3" style="width: 150px; height: 150px; font-size: 3rem; display: flex; align-items: center; justify-content: center;">
                        <?= substr($user['name'], 0, 1) ?>
                    </div>
                <?php endif; ?>

                <h4 class="mb-1"><?= $user['name'] ?></h4>
                <p class="text-muted mb-3"><?= ucfirst($user['role'] ?? 'Admin') ?></p>

                <div class="d-flex justify-content-center gap-2 mb-3">
                    <a href="<?= site_url('profile/edit') ?>" class="btn btn-primary">
                        <i class="bi bi-pencil me-1"></i> Edit Profile
                    </a>
                    <a href="<?= site_url('profile/change-password') ?>" class="btn btn-outline-secondary">
                        <i class="bi bi-lock me-1"></i> Change Password
                    </a>
                </div>

                <div class="d-flex justify-content-center gap-3">
                    <div class="text-center">
                        <h5 class="mb-0"><?= date('d M Y', strtotime($user['created_at'])) ?></h5>
                        <small class="text-muted">Joined Date</small>
                    </div>
                    <div class="text-center">
                        <h5 class="mb-0"><?= $user['last_login'] ? date('d M Y', strtotime($user['last_login'])) : 'N/A' ?></h5>
                        <small class="text-muted">Last Login</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Profile Information</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Full Name</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        <?= $user['name'] ?>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Email</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        <?= $user['email'] ?>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Phone</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        <?= $user['phone'] ?? 'Not provided' ?>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Address</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        <?= $user['address'] ?? 'Not provided' ?>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Role</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        <?= ucfirst($user['role'] ?? 'Admin') ?>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Bio</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        <?= $user['bio'] ?? 'No bio provided' ?>
                    </div>
                </div>
            </div>
        </div>


    </div>
</div>



<?= $this->endSection() ?>
