<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<style>
    /* Premium Dashboard Styles */
    :root {
        --primary-color: #6366F1;
        --primary-dark: #4F46E5;
        --primary-light: #818CF8;
        --secondary-color: #8B5CF6;
        --success-color: #10B981;
        --info-color: #0EA5E9;
        --warning-color: #F59E0B;
        --danger-color: #EF4444;
        --dark-color: #1E293B;
        --light-color: #F8FAFC;
        --gray-100: #F1F5F9;
        --gray-200: #E2E8F0;
        --gray-300: #CBD5E1;
        --gray-400: #94A3B8;
        --gray-500: #64748B;
        --gray-600: #475569;
        --gray-700: #334155;
        --gray-800: #1E293B;
        --gray-900: #0F172A;
    }

    body {
        background-color: #F8FAFC;
    }

    .bg-gradient-primary-to-secondary {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
    }

    .bg-gradient-dark-to-primary {
        background: linear-gradient(135deg, var(--dark-color) 0%, var(--primary-color) 100%) !important;
    }

    .hover-shadow {
        transition: all 0.3s ease;
        border: 1px solid var(--gray-200);
    }

    .hover-shadow:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.08);
        transform: translateY(-2px);
        border-color: var(--primary-light);
    }

    .card {
        transition: all 0.3s ease;
        border-radius: 1rem;
        overflow: hidden;
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .card-header {
        border-bottom: none;
        padding: 1.25rem 1.5rem;
        background-color: transparent;
    }

    .card-body {
        padding: 1.5rem;
    }

    .avatar {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        font-size: 1.25rem;
    }

    .avatar-md {
        width: 48px;
        height: 48px;
        font-size: 1.5rem;
    }

    .progress {
        overflow: hidden;
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 1rem;
        height: 6px;
    }

    .badge {
        padding: 0.35em 0.65em;
        font-weight: 500;
        border-radius: 0.5rem;
    }

    .table th {
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.5px;
        color: var(--gray-600);
    }

    .table-hover tbody tr:hover {
        background-color: rgba(99, 102, 241, 0.04);
    }

    .btn {
        border-radius: 0.5rem;
        padding: 0.5rem 1rem;
        font-weight: 500;
    }

    .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .btn-primary:hover {
        background-color: var(--primary-dark);
        border-color: var(--primary-dark);
    }

    .btn-outline-primary {
        color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .btn-outline-primary:hover {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    .text-primary {
        color: var(--primary-color) !important;
    }

    .bg-primary {
        background-color: var(--primary-color) !important;
    }

    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--gray-800);
        margin-bottom: 1.5rem;
        position: relative;
        padding-left: 1rem;
    }

    .section-title::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 4px;
        background-color: var(--primary-color);
        border-radius: 2px;
    }

    .stat-card {
        border-radius: 1rem;
        border: 1px solid var(--gray-200);
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.08);
        border-color: var(--primary-light);
    }
</style>


<!-- Dashboard Header -->
<div class="card mb-4 border-0 bg-gradient-primary-to-secondary text-white">
    <div class="card-body p-4">
        <div class="row align-items-center">
            <div class="col-12 col-md-7">
                <h2 class="fw-bold text-white mb-2 fs-3 fs-md-2">Welcome back, <?= session()->get('name') ?>!</h2>
                <p class="text-white-50 mb-0">Here's what's happening with your business today.</p>

                <div class="d-flex align-items-center mt-4">
                    <div class="me-4">
                        <div class="d-flex align-items-center">
                            <div class="avatar rounded-circle bg-white bg-opacity-25 p-2 me-2">
                                <i class="bi bi-calendar-check text-white"></i>
                            </div>
                            <div>
                                <span class="text-white-50 small">Today</span>
                                <h5 class="mb-0 text-white"><?= date('d M Y') ?></h5>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="d-flex align-items-center">
                            <div class="avatar rounded-circle bg-white bg-opacity-25 p-2 me-2">
                                <i class="bi bi-clock text-white"></i>
                            </div>
                            <div>
                                <span class="text-white-50 small">Current Month</span>
                                <h5 class="mb-0 text-white"><?= date('F Y') ?></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-5 text-md-end mt-4 mt-md-0">
                <div class="d-flex flex-column align-items-start align-items-md-end">
                    <h5 class="text-white mb-3">Quick Actions</h5>
                    <div class="d-flex flex-wrap justify-content-start justify-content-md-end gap-2">
                        <a href="<?= site_url('invoices/new') ?>" class="btn btn-light">
                            <i class="bi bi-plus-circle me-1"></i><span class="d-none d-sm-inline">New</span> Invoice
                        </a>
                        <a href="<?= site_url('clients/new') ?>" class="btn btn-outline-light">
                            <i class="bi bi-person-plus me-1"></i><span class="d-none d-sm-inline">Add</span> Client
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Key Metrics -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <h3 class="section-title">Key Metrics</h3>
    </div>

    <div class="col-6 col-md-6 col-xl-3">
        <div class="stat-card h-100 hover-shadow">
            <div class="card-body p-4">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <div class="avatar rounded-circle p-3" style="background-color:rgba(99, 101, 242, 0.15);">
                            <i class="bi bi-currency-dollar text-primary"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-0 text-uppercase fs-xs fw-semibold">Total Revenue</h6>
                        <h3 class="mb-0 fw-bold fs-4">₹<?= number_format($totalRevenue, 2) ?></h3>
                    </div>
                </div>
                <div class="d-flex align-items-center">
                    <?php if ($revenueChangeType === 'increase'): ?>
                        <span class="badge bg-success-subtle text-success me-2">
                            <i class="bi bi-graph-up me-1"></i><?= $revenueChange ?>%
                        </span>
                    <?php elseif ($revenueChangeType === 'decrease'): ?>
                        <span class="badge bg-danger-subtle text-danger me-2">
                            <i class="bi bi-graph-down me-1"></i><?= $revenueChange ?>%
                        </span>
                    <?php else: ?>
                        <span class="badge bg-secondary-subtle text-secondary me-2">
                            <i class="bi bi-dash me-1"></i>0%
                        </span>
                    <?php endif; ?>
                    <span class="text-muted small">vs. Previous Month</span>
                </div>
            </div>
        </div>
    </div>

    <div class="col-6 col-md-6 col-xl-3">
        <div class="stat-card h-100 hover-shadow">
            <div class="card-body p-4">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <div class="avatar rounded-circle p-3" style="background-color:rgba(239, 68, 68, 0.15);">
                            <i class="bi bi-receipt text-danger"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-0 text-uppercase fs-xs fw-semibold">Total Invoices</h6>
                        <h3 class="mb-0 fw-bold fs-4"><?= $invoicesCount ?></h3>
                    </div>
                </div>
                <div class="d-flex align-items-center">
                    <div class="progress flex-grow-1" style="height: 8px;">
                        <div class="progress-bar bg-secondary" role="progressbar" style="width: <?= $draftInvoices / max(1, $invoicesCount) * 100 ?>%" title="Draft: <?= $draftInvoices ?>"></div>
                        <div class="progress-bar bg-info" role="progressbar" style="width: <?= $sentInvoices / max(1, $invoicesCount) * 100 ?>%" title="Sent: <?= $sentInvoices ?>"></div>
                        <div class="progress-bar bg-success" role="progressbar" style="width: <?= $paidInvoices / max(1, $invoicesCount) * 100 ?>%" title="Paid: <?= $paidInvoices ?>"></div>
                        <div class="progress-bar bg-danger" role="progressbar" style="width: <?= $cancelInvoices / max(1, $invoicesCount) * 100 ?>%" title="Cancelled: <?= $cancelInvoices ?>"></div>
                    </div>
                    <a href="<?= site_url('invoices') ?>" class="btn btn-sm btn-link ms-2 text-decoration-none">
                        <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-6 col-md-6 col-xl-3">
        <div class="stat-card h-100 hover-shadow">
            <div class="card-body p-4">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <div class="avatar rounded-circle p-3" style="background-color:rgba(16, 185, 129, 0.15);">
                            <i class="bi bi-people-fill text-success"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-0 text-uppercase fs-xs fw-semibold">Active Clients</h6>
                        <h3 class="mb-0 fw-bold fs-4"><?= $clientsCount ?></h3>
                    </div>
                </div>
                <div class="d-flex align-items-center justify-content-between">
                    <span class="badge bg-primary-subtle text-primary">
                        <i class="bi bi-people me-1"></i>Total Clients
                    </span>
                    <a href="<?= site_url('clients') ?>" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-eye me-1"></i>View All
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-6 col-md-6 col-xl-3">
        <div class="stat-card h-100 hover-shadow">
            <div class="card-body p-4">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <div class="avatar rounded-circle p-3" style="background-color:rgba(245, 158, 11, 0.15);">
                            <i class="bi bi-envelope text-warning"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-0 text-uppercase fs-xs fw-semibold">Messages</h6>
                        <h3 class="mb-0 fw-bold fs-4"><?= $unreadMessages ?> <span class="fs-6 fw-normal text-muted">/ <?= $contactCount ?></span></h3>
                    </div>
                </div>
                <div class="d-flex align-items-center justify-content-between">
                    <?php if ($unreadMessages > 0): ?>
                        <span class="badge bg-danger-subtle text-danger">
                            <i class="bi bi-envelope-exclamation me-1"></i>Unread Messages
                        </span>
                    <?php else: ?>
                        <span class="badge bg-success-subtle text-success">
                            <i class="bi bi-check-circle me-1"></i>All Read
                        </span>
                    <?php endif; ?>
                    <a href="<?= site_url('contact') ?>" class="btn btn-sm btn-outline-warning">
                        <i class="bi bi-envelope me-1"></i>View Messages
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Revenue Analysis Section -->
<div class="row mb-4">
    <div class="col-12">
        <h3 class="section-title">Revenue Analysis</h3>
    </div>

    <!-- Revenue Chart -->
    <div class="col-lg-8 mb-4 mb-lg-0">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent d-flex flex-column flex-sm-row justify-content-between align-items-start align-items-sm-center gap-2">
                <h5 class="mb-0 fs-6 fs-md-5">Revenue Trend</h5>
                <div class="d-flex gap-2">
                    <span class="badge bg-primary-subtle text-primary">Last 6 Months</span>
                </div>
            </div>
            <div class="card-body p-4">
                <div style="height: 320px;">
                    <canvas id="revenueChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Revenue Overview -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm bg-gradient-primary-to-secondary text-white h-100">
            <div class="card-header bg-transparent border-0">
                <h5 class="mb-0 text-white">Monthly Revenue</h5>
            </div>
            <div class="card-body p-4">
                <div class="mb-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="avatar rounded-circle bg-white bg-opacity-25 p-3 me-3">
                            <i class="bi bi-calendar-check text-white fs-4"></i>
                        </div>
                        <div>
                            <p class="mb-0 text-white-50 small">Current Month</p>
                            <h3 class="mb-0 fw-bold fs-4">₹<?= number_format($currentMonthRevenue, 2) ?></h3>
                        </div>
                    </div>

                    <div class="d-flex align-items-center mb-3">
                        <div class="avatar rounded-circle bg-white bg-opacity-25 p-3 me-3">
                            <i class="bi bi-calendar-minus text-white fs-4"></i>
                        </div>
                        <div>
                            <p class="mb-0 text-white-50 small">Previous Month</p>
                            <h3 class="mb-0 fw-bold fs-4">₹<?= number_format($previousMonthRevenue, 2) ?></h3>
                        </div>
                    </div>

                    <div class="d-flex align-items-center">
                        <div class="avatar rounded-circle bg-white bg-opacity-25 p-3 me-3">
                            <i class="bi bi-graph-up-arrow text-white fs-4"></i>
                        </div>
                        <div>
                            <p class="mb-0 text-white-50 small">Month-over-Month</p>
                            <h3 class="mb-0 fw-bold fs-4">
                                <?= $revenueChange ?>%
                                <?php if ($revenueChangeType === 'increase'): ?>
                                    <i class="bi bi-arrow-up-right"></i>
                                <?php elseif ($revenueChangeType === 'decrease'): ?>
                                    <i class="bi bi-arrow-down-right"></i>
                                <?php else: ?>
                                    <i class="bi bi-dash"></i>
                                <?php endif; ?>
                            </h3>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="<?= site_url('revenue-reports') ?>" class="btn btn-light">
                        <i class="bi bi-bar-chart-line me-2"></i> View Detailed Reports
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Invoice & Performance Analysis -->
<div class="row mb-4">
    <div class="col-12">
        <h3 class="section-title">Invoice & Performance Analysis</h3>
    </div>

    <!-- Invoice Status Summary -->
    <div class="col-lg-8 mb-4 mb-lg-0">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent d-flex flex-column flex-sm-row justify-content-between align-items-start align-items-sm-center gap-2">
                <h5 class="mb-0 fs-6 fs-md-5">Invoice Status Summary</h5>
                <a href="<?= site_url('invoices/new') ?>" class="btn btn-sm btn-primary">
                    <i class="bi bi-plus-circle me-1"></i> New Invoice
                </a>
            </div>
            <div class="card-body p-4">
                <div class="row g-4 mb-4">
                    <?php foreach (['draft', 'sent', 'paid', 'cancelled'] as $status): ?>
                    <div class="col-6 col-md-3">
                        <div class="d-flex align-items-center p-3 border rounded h-100 hover-shadow">
                            <div class="avatar avatar-md rounded-circle bg-<?= $invoiceStats[$status]['color'] ?> bg-opacity-10 text-<?= $invoiceStats[$status]['color'] ?> me-3">
                                <i class="bi bi-<?= $invoiceStats[$status]['icon'] ?>"></i>
                            </div>
                            <div>
                                <h6 class="mb-0 fs-6"><?= $invoiceStats[$status]['count'] ?> <?= $invoiceStats[$status]['label'] ?></h6>
                                <div class="d-flex flex-column">
                                    <span class="text-muted small"><?= $invoiceStats[$status]['description'] ?></span>
                                    <span class="text-<?= $invoiceStats[$status]['color'] ?> small">₹<?= number_format($invoiceStats[$status]['amount'], 2) ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <div class="row">
                    <div class="col-md-7">
                        <div class="border rounded p-4 h-100 hover-shadow">
                            <h6 class="mb-3 fs-6">Invoice Status Distribution</h6>
                            <div style="height: 220px;">
                                <canvas id="invoiceStatusChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-5">
                        <div class="border rounded p-4 h-100 hover-shadow">
                            <h6 class="mb-3 fs-6">Invoice Status Breakdown</h6>
                            <div class="d-flex flex-column gap-3">
                                <?php foreach (['draft', 'sent', 'paid', 'cancelled'] as $status): ?>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <div class="me-2" style="width: 12px; height: 12px; border-radius: 50%; background-color: var(--<?= $invoiceStats[$status]['color'] ?>-color);"></div>
                                        <span class="text-muted"><?= $invoiceStats[$status]['label'] ?></span>
                                    </div>
                                    <div class="text-end">
                                        <span class="fw-medium"><?= $invoiceStats[$status]['count'] ?></span>
                                        <span class="text-muted ms-1 small">(<?= $invoiceStats[$status]['percentage'] ?>%)</span>
                                    </div>
                                </div>
                                <?php endforeach; ?>

                                <div class="mt-2 pt-2 border-top">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="fw-medium">Total</span>
                                        <span class="fw-medium"><?= $invoiceStats['total']['count'] ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent">
                <h5 class="mb-0 fs-6 fs-md-5">Performance Metrics</h5>
            </div>
            <div class="card-body p-4">
                <div class="d-flex flex-column gap-4">

                    <!-- Pending Revenue -->
                    <div class="card border-0 bg-light">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div class="avatar rounded-circle bg-warning bg-opacity-10 p-2">
                                    <i class="bi bi-hourglass-split text-warning"></i>
                                </div>
                                <span class="badge bg-warning-subtle text-warning rounded-pill">Pending</span>
                            </div>
                            <h6 class="text-muted mb-1 small">Pending Revenue</h6>
                            <h3 class="mb-0 fw-bold fs-4">₹<?= number_format($invoiceStats['sent']['amount'], 2) ?></h3>
                            <div class="progress mt-3" style="height: 4px;">
                                <div class="progress-bar bg-warning" role="progressbar" style="width: 60%"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Conversion Rate -->
                    <div class="card border-0 bg-light">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div class="avatar rounded-circle bg-success bg-opacity-10 p-2">
                                    <i class="bi bi-arrow-repeat text-success"></i>
                                </div>
                                <span class="badge bg-success-subtle text-success rounded-pill">Rate</span>
                            </div>
                            <h6 class="text-muted mb-1 small">Invoice Conversion Rate</h6>
                            <?php
                            $totalSentAndPaid = $sentInvoices + $paidInvoices;
                            $conversionRate = $totalSentAndPaid > 0 ? ($paidInvoices / $totalSentAndPaid) * 100 : 0;
                            ?>
                            <h3 class="mb-0 fw-bold fs-4"><?= number_format($conversionRate, 1) ?>%</h3>
                            <div class="progress mt-3" style="height: 4px;">
                                <div class="progress-bar bg-success" role="progressbar" style="width: <?= min(100, $conversionRate) ?>%"></div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>


<!-- Client & Invoice Activity -->
<div class="row mb-4">
    <div class="col-12">
        <h3 class="section-title">Client & Invoice Activity</h3>
    </div>

    <!-- Top Clients -->
    <div class="col-lg-5 mb-4 mb-lg-0">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent d-flex flex-column flex-sm-row justify-content-between align-items-start align-items-sm-center gap-2">
                <h5 class="mb-0 fs-6 fs-md-5">Top Clients by Revenue</h5>
                <a href="<?= site_url('clients') ?>" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-people me-1"></i> All Clients
                </a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover align-middle mb-0">
                        <thead class="table-light">
                            <tr>
                                <th class="ps-3">Client</th>
                                <th>Revenue</th>
                                <th>Invoices</th>
                                <th class="text-end pe-3">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            // This would normally come from the controller, but we'll simulate it here
                            $topClients = [];

                            if (!empty($recentInvoices)) {
                                $clientRevenue = [];
                                $clientInvoices = [];
                                $clientLastInvoice = [];

                                foreach ($recentInvoices as $invoice) {
                                    $clientId = $invoice['client_id'];
                                    $clientName = $invoice['client_name'];

                                    if (!isset($clientRevenue[$clientId])) {
                                        $clientRevenue[$clientId] = 0;
                                        $clientInvoices[$clientId] = 0;
                                        $clientLastInvoice[$clientId] = '';
                                        $topClients[$clientId] = [
                                            'id' => $clientId,
                                            'name' => $clientName,
                                            'revenue' => 0,
                                            'invoices' => 0,
                                            'last_invoice' => ''
                                        ];
                                    }

                                    if ($invoice['status'] === 'paid') {
                                        $clientRevenue[$clientId] += $invoice['total'];
                                        $topClients[$clientId]['revenue'] = $clientRevenue[$clientId];
                                    }

                                    $clientInvoices[$clientId]++;
                                    $topClients[$clientId]['invoices'] = $clientInvoices[$clientId];

                                    if (empty($clientLastInvoice[$clientId]) || strtotime($invoice['invoice_date']) > strtotime($clientLastInvoice[$clientId])) {
                                        $clientLastInvoice[$clientId] = $invoice['invoice_date'];
                                        $topClients[$clientId]['last_invoice'] = $invoice['invoice_date'];
                                    }
                                }

                                // Sort by revenue (highest first)
                                usort($topClients, function($a, $b) {
                                    return $b['revenue'] <=> $a['revenue'];
                                });

                                // Take top 5
                                $topClients = array_slice($topClients, 0, 5);
                            }

                            if (!empty($topClients)):
                                foreach ($topClients as $client):
                            ?>
                                <tr>
                                    <td class="ps-3">
                                        <div class="d-flex align-items-center">
                                            <div class="avatar avatar-md rounded-circle text-primary me-3" style="background-color:rgba(99, 101, 242, 0.20);">
                                                <span><?= substr($client['name'], 0, 1) ?></span>
                                            </div>
                                            <div>
                                                <h6 class="mb-0 fw-medium"><?= $client['name'] ?></h6>
                                                <span class="text-muted small"><?= date('d M Y', strtotime($client['last_invoice'])) ?></span>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <h6 class="mb-0 fw-semibold">₹<?= number_format($client['revenue'], 2) ?></h6>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary-subtle text-primary rounded-pill"><?= $client['invoices'] ?></span>
                                    </td>
                                    <td class="text-end pe-3">
                                        <a href="<?= site_url('clients/edit/' . $client['id']) ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php
                                endforeach;
                            else:
                            ?>
                                <tr>
                                    <td colspan="4" class="text-center py-4">
                                        <div class="py-3">
                                            <i class="bi bi-people fs-1 text-muted"></i>
                                            <h6 class="mt-3 text-muted">No client data available</h6>
                                            <p class="text-muted small">Start creating invoices to see top clients</p>
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Invoices -->
    <div class="col-lg-7">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent d-flex flex-column flex-sm-row align-items-start align-items-sm-center justify-content-between gap-2">
                <h5 class="mb-0 fs-6 fs-md-5">Recent Invoices</h5>
                <div>
                    <a href="<?= site_url('invoices/new') ?>" class="btn btn-sm btn-primary me-2">
                        <i class="bi bi-plus-circle me-1"></i> New
                    </a>
                    <a href="<?= site_url('invoices') ?>" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-list me-1"></i> View All
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($recentInvoices)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="ps-3">Invoice #</th>
                                    <th>Client</th>
                                    <th class="d-none d-md-table-cell">Date</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th class="text-end pe-3">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentInvoices as $invoice): ?>
                                    <tr>
                                        <td class="ps-3">
                                            <a href="<?= site_url('invoices/view/' . $invoice['id']) ?>" class="fw-medium text-decoration-none">
                                                <?= $invoice['invoice_number'] ?>
                                            </a>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar avatar-sm text-primary rounded-circle me-2" style="background-color:rgba(99, 101, 242, 0.20);">
                                                    <span><?= substr($invoice['client_name'], 0, 1) ?></span>
                                                </div>
                                                <span class="small d-inline-block text-truncate" style="max-width: 120px;"><?= $invoice['client_name'] ?></span>
                                            </div>
                                        </td>
                                        <td class="d-none d-md-table-cell"><span class="small text-muted"><?= date('d M Y', strtotime($invoice['invoice_date'])) ?></span></td>
                                        <td><span class="fw-medium">₹<?= number_format($invoice['total'], 2) ?></span></td>
                                        <td>
                                            <?php
                                            $statusClass = 'secondary';
                                            $statusIcon = 'circle';

                                            switch ($invoice['status']) {
                                                case 'draft':
                                                    $statusClass = 'secondary';
                                                    $statusIcon = 'file-earmark';
                                                    break;
                                                case 'sent':
                                                    $statusClass = 'info';
                                                    $statusIcon = 'envelope';
                                                    break;
                                                case 'paid':
                                                    $statusClass = 'success';
                                                    $statusIcon = 'check-circle';
                                                    break;
                                                case 'cancelled':
                                                    $statusClass = 'danger';
                                                    $statusIcon = 'x-circle';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge bg-<?= $statusClass ?>-subtle text-<?= $statusClass ?> rounded-pill">
                                                <i class="bi bi-<?= $statusIcon ?> me-1 small"></i>
                                                <span class="d-none d-sm-inline"><?= ucfirst($invoice['status']) ?></span>
                                            </span>
                                        </td>
                                        <td class="text-end pe-3">
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-icon" data-bs-toggle="dropdown">
                                                    <i class="bi bi-three-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-end">
                                                    <li><a class="dropdown-item" href="<?= site_url('invoices/view/' . $invoice['id']) ?>"><i class="bi bi-eye me-2"></i>View</a></li>
                                                    <li><a class="dropdown-item" href="<?= site_url('invoices/edit/' . $invoice['id']) ?>"><i class="bi bi-pencil me-2"></i>Edit</a></li>
                                                    <li><a class="dropdown-item" href="<?= site_url('invoices/print/' . $invoice['id']) ?>"><i class="bi bi-printer me-2"></i>Print</a></li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <div class="avatar avatar-lg rounded-circle mx-auto mb-3" style="background-color:rgba(255, 69, 0, 0.1); color: #ff4500;">
                            <i class="bi bi-receipt fs-3"></i>
                        </div>
                        <h6 class="text-muted">No invoices yet</h6>
                        <a href="<?= site_url('invoices/new') ?>" class="btn btn-sm btn-primary mt-3">
                            <i class="bi bi-plus-circle me-1"></i> Create Invoice
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Quick Actions & Activity -->
    <div class="col-lg-5 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent d-flex align-items-center justify-content-between">
                <h5 class="mb-0 fs-6 fs-md-5">Quick Actions</h5>
            </div>
            <div class="card-body p-0">
                <div class="tab-content">
                    <div class="tab-pane fade show active" id="actions-tab">
                        <div class="list-group list-group-flush">
                            <a href="<?= site_url('invoices/new') ?>" class="list-group-item list-group-item-action d-flex align-items-center p-3">
                                <div class="avatar avatar-sm rounded-circle me-3 bg-danger bg-opacity-10">
                                    <i class="bi bi-receipt text-danger"></i>
                                </div>
                                <div class="flex-grow-1 min-width-0">
                                    <h6 class="mb-0 fw-medium text-truncate">Create New Invoice</h6>
                                    <small class="text-muted d-block text-truncate">Generate a new invoice for a client</small>
                                </div>
                                <i class="bi bi-chevron-right ms-2 flex-shrink-0"></i>
                            </a>
                            <a href="<?= site_url('clients/new') ?>" class="list-group-item list-group-item-action d-flex align-items-center p-3">
                                <div class="avatar avatar-sm rounded-circle me-3" style="background-color:rgba(99, 101, 242, 0.20);">
                                    <i class="bi bi-person-plus text-primary"></i>
                                </div>
                                <div class="flex-grow-1 min-width-0">
                                    <h6 class="mb-0 fw-medium text-truncate">Add New Client</h6>
                                    <small class="text-muted d-block text-truncate">Register a new client</small>
                                </div>
                                <i class="bi bi-chevron-right ms-2 flex-shrink-0"></i>
                            </a>
                            <a href="<?= site_url('blog-posts/new') ?>" class="list-group-item list-group-item-action d-flex align-items-center p-3">
                                <div class="avatar avatar-sm rounded-circle me-3 bg-success bg-opacity-10">
                                    <i class="bi bi-file-earmark-text text-success"></i>
                                </div>
                                <div class="flex-grow-1 min-width-0">
                                    <h6 class="mb-0 fw-medium text-truncate">Create Blog Post</h6>
                                    <small class="text-muted d-block text-truncate">Write a new blog article</small>
                                </div>
                                <i class="bi bi-chevron-right ms-2 flex-shrink-0"></i>
                            </a>
                            <a href="<?= site_url('services/new') ?>" class="list-group-item list-group-item-action d-flex align-items-center p-3">
                                <div class="avatar avatar-sm rounded-circle me-3 bg-info bg-opacity-10">
                                    <i class="bi bi-gear text-info"></i>
                                </div>
                                <div class="flex-grow-1 min-width-0">
                                    <h6 class="mb-0 fw-medium text-truncate">Add New Service</h6>
                                    <small class="text-muted d-block text-truncate">Create a new service offering</small>
                                </div>
                                <i class="bi bi-chevron-right ms-2 flex-shrink-0"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Management Section -->
<div class="row mb-4">
    <!-- Blog Stats & Posts -->
    <div class="col-lg-8 mb-4 mb-lg-0">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent d-flex flex-column flex-sm-row justify-content-between align-items-start align-items-sm-center gap-2">
                <h5 class="mb-0 fs-6 fs-md-5">Blog Management</h5>
                <a href="<?= site_url('blog-posts/new') ?>" class="btn btn-sm btn-primary">
                    <i class="bi bi-plus-circle me-1"></i> New Post
                </a>
            </div>
            <div class="card-body p-3 p-md-4">
                <!-- Blog Stats -->
                <div class="row g-3 mb-4">
                    <div class="col-6 col-md-3">
                        <div class="p-3 border rounded bg-light h-100">
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-md rounded-circle text-primary me-2 me-md-3" style="background-color:rgba(99, 101, 242, 0.20);">
                                    <i class="bi bi-file-earmark-text"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0 fw-semibold"><?= $totalPosts ?></h6>
                                    <span class="text-muted small">Total Posts</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-6 col-md-3">
                        <div class="p-3 border rounded bg-light h-100">
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-md rounded-circle bg-success bg-opacity-10 text-success me-2 me-md-3">
                                    <i class="bi bi-check-circle"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0 fw-semibold"><?= $publishedPosts ?></h6>
                                    <span class="text-muted small">Published</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-6 col-md-3">
                        <div class="p-3 border rounded bg-light h-100">
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-md rounded-circle bg-warning bg-opacity-10 text-warning me-2 me-md-3">
                                    <i class="bi bi-pencil-square"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0 fw-semibold"><?= $draftPosts ?></h6>
                                    <span class="text-muted small">Drafts</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-6 col-md-3">
                        <div class="p-3 border rounded bg-light h-100">
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-md rounded-circle bg-info bg-opacity-10 text-info me-2 me-md-3">
                                    <i class="bi bi-chat-dots"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0 fw-semibold"><?= $totalComments ?></h6>
                                    <span class="text-muted small">Comments</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Posts -->
                <h6 class="fw-semibold mb-3">Recent Posts</h6>
                <?php if (empty($recentPosts)): ?>
                    <div class="text-center py-4">
                        <div class="mb-3">
                            <i class="bi bi-file-earmark-text fs-1 text-muted"></i>
                        </div>
                        <h6 class="text-muted">No blog posts yet</h6>
                        <a href="<?= site_url('blog-posts/new') ?>" class="btn btn-sm btn-primary mt-3">
                            <i class="bi bi-plus-circle me-1"></i> Create Post
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th>Title</th>
                                    <th class="d-none d-md-table-cell">Category</th>
                                    <th>Status</th>
                                    <th class="d-none d-md-table-cell">Date</th>
                                    <th class="text-end">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentPosts as $post): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar avatar-sm text-primary rounded-circle me-2" style="background-color:rgba(99, 101, 242, 0.20);">
                                                    <i class="bi bi-file-text"></i>
                                                </div>
                                                <span class="fw-medium d-inline-block text-truncate" style="max-width: 150px;"><?= esc($post['title']) ?></span>
                                            </div>
                                        </td>
                                        <td class="d-none d-md-table-cell"><span class="badge bg-light text-dark"><?= esc($post['category_name']) ?></span></td>
                                        <td>
                                            <span class="badge <?= $post['status'] === 'published' ? 'bg-success-subtle text-success' : 'bg-secondary-subtle text-secondary' ?>">
                                                <?= ucfirst($post['status']) ?>
                                            </span>
                                        </td>
                                        <td class="d-none d-md-table-cell"><span class="small text-muted"><?= date('M d, Y', strtotime($post['created_at'])) ?></span></td>
                                        <td class="text-end">
                                            <a href="<?= site_url('blog-posts/edit/' . $post['id']) ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-pencil"></i><span class="d-none d-sm-inline ms-1">Edit</span>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="<?= site_url('blog-posts') ?>" class="btn btn-sm btn-outline-primary">
                            View All Posts
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Content Management Stats -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent">
                <h5 class="mb-0 fs-6 fs-md-5">Content Overview</h5>
            </div>
            <div class="card-body p-3 p-md-4">
                <div class="list-group list-group-flush">
                    <div class="list-group-item px-0 d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="avatar avatar-sm rounded-circle bg-danger bg-opacity-10 text-danger me-3">
                                <i class="bi bi-sliders"></i>
                            </div>
                            <div class="min-width-0">
                                <h6 class="mb-0 fw-medium text-truncate">Slider Items</h6>
                                <small class="text-muted d-block text-truncate">Homepage carousel</small>
                            </div>
                        </div>
                        <div class="d-flex align-items-center ms-2">
                            <span class="badge bg-light text-dark me-2"><?= $slidersCount ?? 0 ?></span>
                            <a href="<?= site_url('home-slider') ?>" class="btn btn-sm btn-icon">
                                <i class="bi bi-arrow-right text-primary"></i>
                            </a>
                        </div>
                    </div>

                    <div class="list-group-item px-0 d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="avatar avatar-sm rounded-circle text-primary me-3" style="background-color:rgba(99, 101, 242, 0.20);">
                                <i class="bi bi-gear"></i>
                            </div>
                            <div class="min-width-0">
                                <h6 class="mb-0 fw-medium text-truncate">Services</h6>
                                <small class="text-muted d-block text-truncate">Service offerings</small>
                            </div>
                        </div>
                        <div class="d-flex align-items-center ms-2">
                            <span class="badge bg-light text-dark me-2"><?= $servicesCount ?></span>
                            <a href="<?= site_url('services') ?>" class="btn btn-sm btn-icon">
                                <i class="bi bi-arrow-right text-primary"></i>
                            </a>
                        </div>
                    </div>

                    <div class="list-group-item px-0 d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="avatar avatar-sm rounded-circle bg-success bg-opacity-10 text-success me-3">
                                <i class="bi bi-people"></i>
                            </div>
                            <div class="min-width-0">
                                <h6 class="mb-0 fw-medium text-truncate">Team Members</h6>
                                <small class="text-muted d-block text-truncate">Staff profiles</small>
                            </div>
                        </div>
                        <div class="d-flex align-items-center ms-2">
                            <span class="badge bg-light text-dark me-2"><?= $teamCount ?></span>
                            <a href="<?= site_url('team') ?>" class="btn btn-sm btn-icon">
                                <i class="bi bi-arrow-right text-primary"></i>
                            </a>
                        </div>
                    </div>

                    <div class="list-group-item px-0 d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="avatar avatar-sm rounded-circle bg-info bg-opacity-10 text-info me-3">
                                <i class="bi bi-images"></i>
                            </div>
                            <div class="min-width-0">
                                <h6 class="mb-0 fw-medium text-truncate">Gallery Items</h6>
                                <small class="text-muted d-block text-truncate">Image gallery</small>
                            </div>
                        </div>
                        <div class="d-flex align-items-center ms-2">
                            <span class="badge bg-light text-dark me-2"><?= $galleryCount ?></span>
                            <a href="<?= site_url('gallery') ?>" class="btn btn-sm btn-icon">
                                <i class="bi bi-arrow-right text-primary"></i>
                            </a>
                        </div>
                    </div>

                    <div class="list-group-item px-0 d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="avatar avatar-sm rounded-circle bg-warning bg-opacity-10 text-warning me-3">
                                <i class="bi bi-envelope"></i>
                            </div>
                            <div class="min-width-0">
                                <h6 class="mb-0 fw-medium text-truncate">Messages</h6>
                                <small class="text-muted d-block text-truncate">Contact inquiries</small>
                            </div>
                        </div>
                        <div class="d-flex align-items-center ms-2">
                            <span class="badge bg-light text-dark me-2"><?= $contactCount ?></span>
                            <a href="<?= site_url('contact') ?>" class="btn btn-sm btn-icon">
                                <i class="bi bi-arrow-right text-primary"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <div class="d-grid gap-2">
                        <a href="<?= site_url('about') ?>" class="btn btn-outline-primary">
                            <i class="bi bi-info-circle me-2"></i>Manage About Page
                        </a>
                        <a href="<?= site_url('contact-info') ?>" class="btn btn-outline-primary">
                            <i class="bi bi-geo-alt me-2"></i>Manage Contact Info
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');

    // Parse the PHP data for the chart
    const revenueData = <?= json_encode($monthlyRevenueData) ?>;
    const revenueLabels = <?= json_encode($monthlyRevenueLabels) ?>;

    // Invoice Status Chart
    const invoiceStatusCtx = document.getElementById('invoiceStatusChart').getContext('2d');

    // Check if invoiceStats is defined
    <?php if (isset($invoiceStats)): ?>
    const invoiceStatusData = [
        <?= $invoiceStats['draft']['amount'] ?? 0 ?>,
        <?= $invoiceStats['sent']['amount'] ?? 0 ?>,
        <?= $invoiceStats['paid']['amount'] ?? 0 ?>,
        <?= $invoiceStats['cancelled']['amount'] ?? 0 ?>
    ];

    // For tooltip formatting
    const invoiceStatusCounts = [
        <?= $invoiceStats['draft']['count'] ?? 0 ?>,
        <?= $invoiceStats['sent']['count'] ?? 0 ?>,
        <?= $invoiceStats['paid']['count'] ?? 0 ?>,
        <?= $invoiceStats['cancelled']['count'] ?? 0 ?>
    ];
    <?php else: ?>
    // Default values if invoiceStats is not defined
    const invoiceStatusData = [0, 0, 0, 0];
    const invoiceStatusCounts = [0, 0, 0, 0];
    console.error('Invoice stats data is not available');
    <?php endif; ?>

    const invoiceStatusChart = new Chart(invoiceStatusCtx, {
        type: 'doughnut',
        data: {
            labels: ['Draft', 'Sent', 'Paid', 'Cancelled'],
            datasets: [{
                data: invoiceStatusData,
                backgroundColor: [
                    'rgba(107, 114, 128, 0.8)',  // Secondary/Gray
                    'rgba(14, 165, 233, 0.8)',   // Info/Blue
                    'rgba(16, 185, 129, 0.8)',   // Success/Green
                    'rgba(239, 68, 68, 0.8)'     // Danger/Red
                ],
                borderColor: [
                    'rgba(107, 114, 128, 1)',
                    'rgba(14, 165, 233, 1)',
                    'rgba(16, 185, 129, 1)',
                    'rgba(239, 68, 68, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 10,
                        font: {
                            size: 11
                        }
                    }
                },
                tooltip: {
                    backgroundColor: '#fff',
                    titleColor: '#1e293b',
                    bodyColor: '#1e293b',
                    borderColor: '#e2e8f0',
                    borderWidth: 1,
                    padding: 10,
                    displayColors: true,
                    callbacks: {
                        label: function(context) {
                            try {
                                const label = context.label || '';
                                const value = context.parsed || 0;
                                const count = invoiceStatusCounts[context.dataIndex] || 0;
                                const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
                                const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                return [
                                    `${label}: ${count} invoice${count !== 1 ? 's' : ''}`,
                                    `Amount: ₹${value.toLocaleString('en-IN')}`,
                                    `${percentage}% of total revenue`
                                ];
                            } catch (error) {
                                console.error('Error in tooltip callback:', error);
                                return 'Error displaying data';
                            }
                        }
                    }
                }
            },
            cutout: '60%'
        }
    });

    const revenueChart = new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: revenueLabels,
            datasets: [{
                label: 'Monthly Revenue (₹)',
                data: revenueData,
                backgroundColor: 'rgba(99, 102, 241, 0.1)',
                borderColor: '#6366f1',
                borderWidth: 3,
                pointBackgroundColor: '#ffffff',
                pointBorderColor: '#6366f1',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8,
                pointHoverBackgroundColor: '#6366f1',
                pointHoverBorderColor: '#ffffff',
                pointHoverBorderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            size: 12,
                            weight: 'bold'
                        }
                    }
                },
                tooltip: {
                    backgroundColor: '#fff',
                    titleColor: '#1e293b',
                    bodyColor: '#1e293b',
                    borderColor: '#e2e8f0',
                    borderWidth: 1,
                    padding: 12,
                    displayColors: true,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            let value = context.parsed.y;
                            return `${label}: ₹${value.toLocaleString('en-IN')}`;
                        },
                        title: function(context) {
                            return context[0].label;
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 11
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            borderDash: [2, 2]
                        },
                        ticks: {
                            font: {
                                size: 11
                            },
                            callback: function(value) {
                                return '₹' + value.toLocaleString('en-IN');
                            }
                        }
                    }
                }
            }
        }
    });
});
</script>

<?= $this->endSection() ?>

