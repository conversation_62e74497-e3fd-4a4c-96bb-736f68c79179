<?php

namespace App\Controllers\Api;

use App\Models\GalleryModel;
use CodeIgniter\HTTP\ResponseInterface;

class Gallery extends BaseController
{
    protected $galleryModel;

    public function __construct()
    {
        $this->galleryModel = new GalleryModel();
    }

    public function index()
    {
        $gallery = $this->galleryModel->where('status', 'active')->findAll();

        foreach ($gallery as &$item) {
            if ($item['image']) {
                $item['image_url'] = base_url('public/uploads/gallery/' . $item['image']);
            }
        }

        return $this->respondWithSuccess($gallery, 'Gallery items retrieved successfully');
    }

    public function show($id = null)
    {
        $item = $this->galleryModel->find($id);

        if (!$item) {
            return $this->respondWithError('Gallery item not found', ResponseInterface::HTTP_NOT_FOUND);
        }

        if ($item['image']) {
            $item['image_url'] = base_url('public/uploads/gallery/' . $item['image']);
        }

        return $this->respondWithSuccess($item, 'Gallery item retrieved successfully');
    }

    public function create()
    {
        // Validate form data first
        $rules = $this->galleryModel->validationRules;

        if (!$this->validate($rules)) {
            return $this->respondWithError($this->validator->getErrors());
        }

        // Handle file upload
        $image = $this->request->getFile('image');
        $imageName = null;

        // Check if image is uploaded and valid
        if ($image && $image->isValid() && !$image->hasMoved()) {
            // Validate the image separately
            $imageValidation = $this->validate([
                'image' => 'is_image[image]|mime_in[image,image/jpg,image/jpeg,image/png,image/gif]'
            ]);

            if (!$imageValidation) {
                return $this->respondWithError($this->validator->getErrors());
            }

            // Create upload directory if it doesn't exist
            $uploadPath = ROOTPATH . 'public/uploads/gallery';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0777, true);
            }

            $imageName = $image->getRandomName();
            $image->move($uploadPath, $imageName);
        } else {
            return $this->respondWithError(['image' => 'Please upload a valid image file']);
        }

        $data = [
            'image'  => $imageName,
            'status' => $this->request->getPost('status') ?? 'active',
        ];

        $id = $this->galleryModel->insert($data);
        $item = $this->galleryModel->find($id);

        return $this->respondWithSuccess($item, 'Gallery item created successfully', ResponseInterface::HTTP_CREATED);
    }

    public function update($id = null)
    {
        $item = $this->galleryModel->find($id);

        if (!$item) {
            return $this->respondWithError('Gallery item not found', ResponseInterface::HTTP_NOT_FOUND);
        }

        // Validate form data first
        $rules = $this->galleryModel->validationRules;

        if (!$this->validate($rules)) {
            return $this->respondWithError($this->validator->getErrors());
        }

        // Handle file upload
        $image = $this->request->getFile('image');
        $imageName = $item['image']; // Keep existing image by default

        // Check if a new image is uploaded
        if ($image && $image->isValid() && !$image->hasMoved()) {
            // Validate the image separately
            $imageValidation = $this->validate([
                'image' => 'is_image[image]|mime_in[image,image/jpg,image/jpeg,image/png,image/gif]'
            ]);

            if (!$imageValidation) {
                return $this->respondWithError($this->validator->getErrors());
            }

            // Create upload directory if it doesn't exist
            $uploadPath = ROOTPATH . 'public/uploads/gallery';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0777, true);
            }

            // Delete old image if exists
            if ($item['image'] && file_exists($uploadPath . '/' . $item['image'])) {
                unlink($uploadPath . '/' . $item['image']);
            }

            $imageName = $image->getRandomName();
            $image->move($uploadPath, $imageName);
        }

        $data = [
            'image'  => $imageName,
            'status' => $this->request->getPost('status'),
        ];

        $this->galleryModel->update($id, $data);
        $item = $this->galleryModel->find($id);

        return $this->respondWithSuccess($item, 'Gallery item updated successfully');
    }

    public function delete($id = null)
    {
        $item = $this->galleryModel->find($id);

        if (!$item) {
            return $this->respondWithError('Gallery item not found', ResponseInterface::HTTP_NOT_FOUND);
        }

        // Delete image if exists
        if ($item['image'] && file_exists(ROOTPATH . 'public/uploads/gallery/' . $item['image'])) {
            unlink(ROOTPATH . 'public/uploads/gallery/' . $item['image']);
        }

        $this->galleryModel->delete($id);

        return $this->respondWithSuccess(null, 'Gallery item deleted successfully');
    }
}
