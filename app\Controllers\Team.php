<?php

namespace App\Controllers;

use App\Models\TeamModel;
use CodeIgniter\Exceptions\PageNotFoundException;

class Team extends BaseController
{
    protected $teamModel;

    public function __construct()
    {
        $this->teamModel = new TeamModel();
    }

    public function index()
    {
        $data = [
            'title' => 'Manage Team Members',
            'team'  => $this->teamModel->orderBy('id', 'DESC')->paginate(10, 'team'),
            'pager' => $this->teamModel->pager,
        ];

        return view('admin/team/index', $data);
    }

    public function new()
    {
        $data = [
            'title' => 'Add New Team Member',
        ];

        return view('admin/team/create', $data);
    }

    public function create()
    {
        if (!$this->validate($this->teamModel->validationRules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Handle file upload
        $image = $this->request->getFile('image');
        $imageName = null;

        if ($image && $image->isValid() && !$image->hasMoved()) {
            $imageName = $image->getRandomName();
            $image->move(ROOTPATH . 'public/uploads/team', $imageName);
        }

        $data = [
            'name'      => $this->request->getPost('name'),
            'position'  => $this->request->getPost('position'),
            'bio'       => $this->request->getPost('bio'),
            'image'     => $imageName,
            'facebook'  => $this->request->getPost('facebook'),
            'twitter'   => $this->request->getPost('twitter'),
            'linkedin'  => $this->request->getPost('linkedin'),
            'instagram' => $this->request->getPost('instagram'),
            'status'    => $this->request->getPost('status'),
        ];

        $this->teamModel->insert($data);
        return redirect()->to('/team')->with('message', 'Team member added successfully');
    }

    public function edit($id = null)
    {
        $member = $this->teamModel->find($id);

        if (!$member) {
            throw new PageNotFoundException('Team member not found');
        }

        $data = [
            'title'  => 'Edit Team Member',
            'member' => $member,
        ];

        return view('admin/team/edit', $data);
    }

    public function update($id = null)
    {
        $member = $this->teamModel->find($id);

        if (!$member) {
            throw new PageNotFoundException('Team member not found');
        }

        if (!$this->validate($this->teamModel->validationRules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Handle file upload
        $image = $this->request->getFile('image');
        $imageName = $member['image'];

        if ($image && $image->isValid() && !$image->hasMoved()) {
            // Delete old image if exists
            if ($member['image'] && file_exists(ROOTPATH . 'public/uploads/team/' . $member['image'])) {
                unlink(ROOTPATH . 'public/uploads/team/' . $member['image']);
            }

            $imageName = $image->getRandomName();
            $image->move(ROOTPATH . 'public/uploads/team', $imageName);
        }

        $data = [
            'name'      => $this->request->getPost('name'),
            'position'  => $this->request->getPost('position'),
            'bio'       => $this->request->getPost('bio'),
            'image'     => $imageName,
            'facebook'  => $this->request->getPost('facebook'),
            'twitter'   => $this->request->getPost('twitter'),
            'linkedin'  => $this->request->getPost('linkedin'),
            'instagram' => $this->request->getPost('instagram'),
            'status'    => $this->request->getPost('status'),
        ];

        $this->teamModel->update($id, $data);
        return redirect()->to('/team')->with('message', 'Team member updated successfully');
    }

    public function delete($id = null)
    {
        $member = $this->teamModel->find($id);

        if (!$member) {
            throw new PageNotFoundException('Team member not found');
        }

        // Delete image if exists
        if ($member['image'] && file_exists(ROOTPATH . 'public/uploads/team/' . $member['image'])) {
            unlink(ROOTPATH . 'public/uploads/team/' . $member['image']);
        }

        $this->teamModel->delete($id);
        return redirect()->to('/team')->with('message', 'Team member deleted successfully');
    }
}
