<?php

namespace App\Controllers;

use App\Models\SettingsModel;
use CodeIgniter\Exceptions\PageNotFoundException;

class Settings extends BaseController
{
    protected $settingsModel;

    public function __construct()
    {
        $this->settingsModel = new SettingsModel();
    }

    public function index()
    {
        $userId = session()->get('id');
        $settings = $this->settingsModel->getUserSettings($userId);

        $data = [
            'title'    => 'Settings',
            'settings' => $settings,
        ];

        return view('admin/settings/index', $data);
    }

    public function update()
    {
        $userId = session()->get('id');

        $rules = [
            'email_notifications' => 'required|in_list[0,1]',
            'push_notifications'  => 'required|in_list[0,1]',
            'theme'               => 'required|in_list[light,dark]',
            'language'            => 'required|in_list[en,es,fr,de]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'email_notifications' => $this->request->getPost('email_notifications'),
            'push_notifications'  => $this->request->getPost('push_notifications'),
            'theme'               => $this->request->getPost('theme'),
            'language'            => $this->request->getPost('language'),
        ];

        $this->settingsModel->updateUserSettings($userId, $data);

        return redirect()->to('settings')->with('message', 'Settings updated successfully');
    }
}
