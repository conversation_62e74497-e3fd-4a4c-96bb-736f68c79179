<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-end align-items-center mb-4">
    <a href="<?= site_url('invoices/new') ?>" class="btn btn-primary">
        <i class="bi bi-plus-circle"></i> Create New Invoice
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Invoice List</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>Invoice #</th>
                        <th>Client</th>
                        <th>Date</th>
                        <th>Subject</th>
                        <th>Amount</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($invoices)): ?>
                        <tr>
                            <td colspan="7" class="text-center">No invoices found</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($invoices as $invoice): ?>
                            <tr>
                                <td><?= $invoice['invoice_number'] ?></td>
                                <td><?= $invoice['client_name'] ?></td>
                                <td><?= date('d/m/Y', strtotime($invoice['invoice_date'])) ?></td>
                                <td><?= $invoice['subject'] ?></td>
                                <td>₹<?= number_format($invoice['total'], 2) ?></td>
                                <td>
                                    <?php
                                    $statusClass = [
                                        'draft' => 'bg-secondary',
                                        'sent' => 'bg-primary',
                                        'paid' => 'bg-success',
                                        'cancelled' => 'bg-danger'
                                    ];
                                    ?>
                                    <span class="badge <?= $statusClass[$invoice['status']] ?? 'bg-secondary' ?>">
                                        <?= ucfirst($invoice['status']) ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="dropdown">
                                        <a class="btn btn-sm btn-primary" href="<?= site_url('invoices/view/' . $invoice['id']) ?>">
                                            <i class="bi bi-eye"></i> View
                                        </a>

                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <?php if (!empty($pager)): ?>
            <?= $pager->links('default', 'admin_full') ?>
        <?php endif; ?>
    </div>
</div>


<?= $this->endSection() ?>
