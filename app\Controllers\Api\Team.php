<?php

namespace App\Controllers\Api;

use App\Models\TeamModel;
use CodeIgniter\HTTP\ResponseInterface;

class Team extends BaseController
{
    protected $teamModel;

    public function __construct()
    {
        $this->teamModel = new TeamModel();
    }

    public function index()
    {
        $team = $this->teamModel->where('status', 'active')->findAll();
        
        foreach ($team as &$member) {
            if ($member['image']) {
                $member['image_url'] = base_url('public/uploads/team/' . $member['image']);
            }
        }

        return $this->respondWithSuccess($team, 'Team members retrieved successfully');
    }

    public function show($id = null)
    {
        $member = $this->teamModel->find($id);

        if (!$member) {
            return $this->respondWithError('Team member not found', ResponseInterface::HTTP_NOT_FOUND);
        }

        if ($member['image']) {
            $member['image_url'] = base_url('public/uploads/team/' . $member['image']);
        }

        return $this->respondWithSuccess($member, 'Team member retrieved successfully');
    }

    public function create()
    {
        $rules = $this->teamModel->validationRules;
        
        if (!$this->validate($rules)) {
            return $this->respondWithError($this->validator->getErrors());
        }

        // Handle file upload
        $image = $this->request->getFile('image');
        $imageName = null;

        if ($image && $image->isValid() && !$image->hasMoved()) {
            $imageName = $image->getRandomName();
            $image->move(ROOTPATH . 'public/uploads/team', $imageName);
        }

        $data = [
            'name'      => $this->request->getPost('name'),
            'position'  => $this->request->getPost('position'),
            'bio'       => $this->request->getPost('bio'),
            'image'     => $imageName,
            'facebook'  => $this->request->getPost('facebook'),
            'twitter'   => $this->request->getPost('twitter'),
            'linkedin'  => $this->request->getPost('linkedin'),
            'instagram' => $this->request->getPost('instagram'),
            'status'    => $this->request->getPost('status') ?? 'active',
        ];

        $id = $this->teamModel->insert($data);
        $member = $this->teamModel->find($id);

        return $this->respondWithSuccess($member, 'Team member created successfully', ResponseInterface::HTTP_CREATED);
    }

    public function update($id = null)
    {
        $member = $this->teamModel->find($id);

        if (!$member) {
            return $this->respondWithError('Team member not found', ResponseInterface::HTTP_NOT_FOUND);
        }

        $rules = $this->teamModel->validationRules;
        
        if (!$this->validate($rules)) {
            return $this->respondWithError($this->validator->getErrors());
        }

        // Handle file upload
        $image = $this->request->getFile('image');
        $imageName = $member['image'];

        if ($image && $image->isValid() && !$image->hasMoved()) {
            // Delete old image if exists
            if ($member['image'] && file_exists(ROOTPATH . 'public/uploads/team/' . $member['image'])) {
                unlink(ROOTPATH . 'public/uploads/team/' . $member['image']);
            }
            
            $imageName = $image->getRandomName();
            $image->move(ROOTPATH . 'public/uploads/team', $imageName);
        }

        $data = [
            'name'      => $this->request->getPost('name'),
            'position'  => $this->request->getPost('position'),
            'bio'       => $this->request->getPost('bio'),
            'image'     => $imageName,
            'facebook'  => $this->request->getPost('facebook'),
            'twitter'   => $this->request->getPost('twitter'),
            'linkedin'  => $this->request->getPost('linkedin'),
            'instagram' => $this->request->getPost('instagram'),
            'status'    => $this->request->getPost('status'),
        ];

        $this->teamModel->update($id, $data);
        $member = $this->teamModel->find($id);

        return $this->respondWithSuccess($member, 'Team member updated successfully');
    }

    public function delete($id = null)
    {
        $member = $this->teamModel->find($id);

        if (!$member) {
            return $this->respondWithError('Team member not found', ResponseInterface::HTTP_NOT_FOUND);
        }

        // Delete image if exists
        if ($member['image'] && file_exists(ROOTPATH . 'public/uploads/team/' . $member['image'])) {
            unlink(ROOTPATH . 'public/uploads/team/' . $member['image']);
        }

        $this->teamModel->delete($id);

        return $this->respondWithSuccess(null, 'Team member deleted successfully');
    }
}
