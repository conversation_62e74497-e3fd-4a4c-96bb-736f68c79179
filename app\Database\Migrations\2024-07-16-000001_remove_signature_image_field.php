<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class RemoveSignatureImageField extends Migration
{
    public function up()
    {
        // Drop the signature_image column as we're now using a permanent signature
        $this->forge->dropColumn('invoices', 'signature_image');
    }

    public function down()
    {
        // Add back the signature_image column if needed
        $fields = [
            'signature_image' => [
                'type' => 'VARCHAR',
                'constraint' => '255',
                'null' => true,
                'after' => 'qr_code'
            ],
        ];
        
        $this->forge->addColumn('invoices', $fields);
    }
}
