<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-end align-items-center mb-4">
    <a href="<?= site_url('blog-categories/new') ?>" class="btn btn-primary">
        <i class="bi bi-plus-circle me-1"></i> Add New Category
    </a>
</div>

<?= $this->include('admin/partials/alerts') ?>

<div class="card">
    <div class="card-body">
        <?php if (empty($categories)): ?>
            <div class="text-center py-5">
                <div class="mb-3">
                    <i class="bi bi-folder-x fs-1 text-muted"></i>
                </div>
                <h5>No Categories Found</h5>
                <p class="text-muted">Get started by creating a new category.</p>
                <a href="<?= site_url('blog-categories/new') ?>" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-1"></i> Add New Category
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead>
                        <tr>
                            <th width="50">#</th>
                            <th>Name</th>
                            <th>Slug</th>
                            <th>Description</th>
                            <th width="150">Created</th>
                            <th width="120">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($categories as $index => $category): ?>
                            <tr>
                                <td><?= ($index + 1) + (10 * ($pager->getCurrentPage() - 1)) ?></td>
                                <td><?= esc($category['name']) ?></td>
                                <td><code><?= esc($category['slug']) ?></code></td>
                                <td>
                                    <?php if (!empty($category['description'])): ?>
                                        <?= character_limiter(esc($category['description']), 50) ?>
                                    <?php else: ?>
                                        <span class="text-muted fst-italic">No description</span>
                                    <?php endif; ?>
                                </td>
                                <td><?= date('M d, Y', strtotime($category['created_at'])) ?></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="<?= site_url('blog-categories/edit/' . $category['id']) ?>" class="btn btn-outline-primary">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="<?= site_url('blog-categories/delete/' . $category['id']) ?>" class="btn btn-outline-danger" onclick="return confirm('Are you sure you want to delete this category?')">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <div class="mt-4">
                <?= $pager->links('categories', 'admin_full') ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>
