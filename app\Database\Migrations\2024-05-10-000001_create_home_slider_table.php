<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateHomeSliderTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'message' => [
                'type'       => 'VARCHAR',
                'constraint' => '200',
            ],
            'paragraph' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'image' => [
                'type'       => 'VARCHAR',
                'constraint' => '255',
            ],
            'order' => [
                'type'       => 'INT',
                'constraint' => 5,
                'default'    => 0,
            ],
            'status' => [
                'type'       => 'ENUM',
                'constraint' => ['active', 'inactive'],
                'default'    => 'active',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->createTable('home_slider');
    }

    public function down()
    {
        $this->forge->dropTable('home_slider');
    }
}
