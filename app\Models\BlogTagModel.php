<?php

namespace App\Models;

use CodeIgniter\Model;

class BlogTagModel extends Model
{
    protected $table            = 'blog_tags';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['name', 'slug'];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules      = [
        'name' => 'required|min_length[2]|max_length[100]',
        'slug' => 'required|min_length[2]|max_length[100]|is_unique[blog_tags.slug,id,{id}]',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Get tags with post count
     *
     * @return array
     */
    public function getTagsWithPostCount()
    {
        $builder = $this->db->table('blog_tags bt');
        $builder->select('bt.*, COUNT(bpt.post_id) as post_count');
        $builder->join('blog_post_tags bpt', 'bpt.tag_id = bt.id', 'left');
        $builder->join('blog_posts bp', 'bp.id = bpt.post_id AND bp.status = "published"', 'left');
        $builder->groupBy('bt.id');
        $builder->orderBy('bt.name', 'ASC');

        return $builder->get()->getResultArray();
    }

    /**
     * Get tags for a specific post
     *
     * @param int $postId
     * @return array
     */
    public function getTagsByPostId($postId)
    {
        $builder = $this->db->table('blog_tags bt');
        $builder->select('bt.*');
        $builder->join('blog_post_tags bpt', 'bpt.tag_id = bt.id');
        $builder->where('bpt.post_id', $postId);
        $builder->orderBy('bt.name', 'ASC');

        return $builder->get()->getResultArray();
    }

    /**
     * Save tags for a post
     *
     * @param int $postId
     * @param array $tagIds
     * @return bool
     */
    public function savePostTags($postId, $tagIds)
    {
        try {
            // Delete existing tags for this post
            $this->db->table('blog_post_tags')->where('post_id', $postId)->delete();

            // Insert new tags
            if (!empty($tagIds)) {
                $data = [];
                foreach ($tagIds as $tagId) {
                    if (!empty($tagId)) {
                        $data[] = [
                            'post_id' => $postId,
                            'tag_id'  => $tagId,
                        ];
                    }
                }

                if (!empty($data)) {
                    return $this->db->table('blog_post_tags')->insertBatch($data);
                }
            }

            return true;
        } catch (\Exception $e) {
            // Log the error
            log_message('error', 'Failed to save post tags: ' . $e->getMessage());
            return false;
        }
    }
}
