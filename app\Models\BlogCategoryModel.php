<?php

namespace App\Models;

use CodeIgniter\Model;

class BlogCategoryModel extends Model
{
    protected $table            = 'blog_categories';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['name', 'slug', 'description'];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules      = [
        'name' => 'required|min_length[3]|max_length[100]',
        'slug' => 'required|min_length[3]|max_length[100]|is_unique[blog_categories.slug,id,{id}]',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Get category with post count
     *
     * @return array
     */
    public function getCategoriesWithPostCount()
    {
        $builder = $this->db->table('blog_categories bc');
        $builder->select('bc.*, COUNT(bp.id) as post_count');
        $builder->join('blog_posts bp', 'bp.category_id = bc.id', 'left');
        $builder->where('bp.status', 'published');
        $builder->orWhere('bp.id IS NULL');
        $builder->groupBy('bc.id');
        $builder->orderBy('bc.name', 'ASC');
        
        return $builder->get()->getResultArray();
    }
}
