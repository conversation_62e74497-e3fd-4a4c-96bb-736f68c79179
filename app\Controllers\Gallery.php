<?php

namespace App\Controllers;

use App\Models\GalleryModel;
use CodeIgniter\Exceptions\PageNotFoundException;

class Gallery extends BaseController
{
    protected $galleryModel;

    public function __construct()
    {
        $this->galleryModel = new GalleryModel();
    }

    public function index()
    {
        $data = [
            'title'   => 'Manage Gallery',
            'gallery' => $this->galleryModel->orderBy('id', 'DESC')->paginate(10, 'gallery'),
            'pager'   => $this->galleryModel->pager,
        ];

        return view('admin/gallery/index', $data);
    }

    public function new()
    {
        $data = [
            'title' => 'Add New Gallery Item',
        ];

        return view('admin/gallery/create', $data);
    }

    public function create()
    {
        // Validate form data
        if (!$this->validate($this->galleryModel->validationRules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Handle file upload
        $image = $this->request->getFile('image');
        $imageName = null;

        // Check if image is uploaded and valid
        if ($image && $image->isValid() && !$image->hasMoved()) {
            // Validate the image separately
            $imageValidation = $this->validate([
                'image' => 'is_image[image]|mime_in[image,image/jpg,image/jpeg,image/png,image/gif]'
            ]);

            if (!$imageValidation) {
                return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
            }

            // Create upload directory if it doesn't exist
            $uploadPath = ROOTPATH . 'public/uploads/gallery';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0777, true);
            }

            $imageName = $image->getRandomName();
            $image->move($uploadPath, $imageName);
        } else {
            return redirect()->back()->withInput()->with('errors', ['image' => 'Please upload a valid image file']);
        }

        $data = [
            'image'  => $imageName,
            'status' => $this->request->getPost('status'),
        ];

        $this->galleryModel->insert($data);
        return redirect()->to('/gallery')->with('message', 'Gallery item added successfully');
    }

    public function edit($id = null)
    {
        $item = $this->galleryModel->find($id);

        if (!$item) {
            throw new PageNotFoundException('Gallery item not found');
        }

        $data = [
            'title' => 'Edit Gallery Item',
            'item'  => $item,
        ];

        return view('admin/gallery/edit', $data);
    }

    public function update($id = null)
    {
        $item = $this->galleryModel->find($id);

        if (!$item) {
            throw new PageNotFoundException('Gallery item not found');
        }

        // Validate form data
        if (!$this->validate($this->galleryModel->validationRules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Handle file upload
        $image = $this->request->getFile('image');
        $imageName = $item['image']; // Keep existing image by default

        // Check if a new image is uploaded
        if ($image && $image->isValid() && !$image->hasMoved()) {
            // Validate the image separately
            $imageValidation = $this->validate([
                'image' => 'is_image[image]|mime_in[image,image/jpg,image/jpeg,image/png,image/gif]'
            ]);

            if (!$imageValidation) {
                return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
            }

            // Create upload directory if it doesn't exist
            $uploadPath = ROOTPATH . 'public/uploads/gallery';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0777, true);
            }

            // Delete old image if exists
            if ($item['image'] && file_exists($uploadPath . '/' . $item['image'])) {
                unlink($uploadPath . '/' . $item['image']);
            }

            $imageName = $image->getRandomName();
            $image->move($uploadPath, $imageName);
        }

        $data = [
            'image'  => $imageName,
            'status' => $this->request->getPost('status'),
        ];

        $this->galleryModel->update($id, $data);
        return redirect()->to('/gallery')->with('message', 'Gallery item updated successfully');
    }

    public function delete($id = null)
    {
        $item = $this->galleryModel->find($id);

        if (!$item) {
            throw new PageNotFoundException('Gallery item not found');
        }

        // Delete image if exists
        if ($item['image'] && file_exists(ROOTPATH . 'public/uploads/gallery/' . $item['image'])) {
            unlink(ROOTPATH . 'public/uploads/gallery/' . $item['image']);
        }

        $this->galleryModel->delete($id);
        return redirect()->to('/gallery')->with('message', 'Gallery item deleted successfully');
    }
}
