<?php

namespace App\Commands;

use App\Models\InvoiceModel;
use App\Models\MonthlyRevenueModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class CalculateMonthlyRevenue extends BaseCommand
{
    protected $group       = 'Revenue';
    protected $name        = 'revenue:calculate';
    protected $description = 'Calculate and save monthly revenue data';

    public function run(array $params)
    {
        $monthlyRevenueModel = new MonthlyRevenueModel();
        $invoiceModel = new InvoiceModel();
        
        // Get current year and month
        $year = date('Y');
        $month = date('m');
        
        // Check if we're on the first day of the month
        $isFirstDayOfMonth = (date('d') === '01');
        
        // If it's the first day of the month, calculate previous month's revenue
        if ($isFirstDayOfMonth) {
            // Calculate previous month
            $prevMonth = $month - 1;
            $prevYear = $year;
            
            // Handle January case
            if ($prevMonth === 0) {
                $prevMonth = 12;
                $prevYear = $year - 1;
            }
            
            // Get start and end dates for previous month
            $startDate = sprintf('%04d-%02d-01', $prevYear, $prevMonth);
            $endDate = date('Y-m-t', strtotime($startDate));
            
            // Get invoice statistics for the previous month
            $stats = $invoiceModel->getInvoiceStatusStats($startDate, $endDate);
            
            // Prepare data for saving
            $data = [
                'total_revenue' => $stats['paid']['amount'],
                'draft_count' => $stats['draft']['count'],
                'draft_amount' => $stats['draft']['amount'],
                'sent_count' => $stats['sent']['count'],
                'sent_amount' => $stats['sent']['amount'],
                'paid_count' => $stats['paid']['count'],
                'paid_amount' => $stats['paid']['amount'],
                'cancelled_count' => $stats['cancelled']['count'],
                'cancelled_amount' => $stats['cancelled']['amount'],
            ];
            
            // Save previous month's revenue
            $monthlyRevenueModel->saveMonthlyRevenue($prevYear, $prevMonth, $data);
            
            CLI::write("Previous month's revenue calculated and saved: $prevYear-$prevMonth", 'green');
        }
        
        // Always calculate current month's revenue
        $startDate = sprintf('%04d-%02d-01', $year, $month);
        $endDate = date('Y-m-t', strtotime($startDate));
        
        // Get invoice statistics for the current month
        $stats = $invoiceModel->getInvoiceStatusStats($startDate, $endDate);
        
        // Prepare data for saving
        $data = [
            'total_revenue' => $stats['paid']['amount'],
            'draft_count' => $stats['draft']['count'],
            'draft_amount' => $stats['draft']['amount'],
            'sent_count' => $stats['sent']['count'],
            'sent_amount' => $stats['sent']['amount'],
            'paid_count' => $stats['paid']['count'],
            'paid_amount' => $stats['paid']['amount'],
            'cancelled_count' => $stats['cancelled']['count'],
            'cancelled_amount' => $stats['cancelled']['amount'],
        ];
        
        // Save current month's revenue
        $monthlyRevenueModel->saveMonthlyRevenue($year, $month, $data);
        
        CLI::write("Current month's revenue calculated and saved: $year-$month", 'green');
    }
}
